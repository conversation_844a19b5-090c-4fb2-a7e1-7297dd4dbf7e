import 'package:flutter/material.dart';

class IconPicker extends StatefulWidget {
  final String? selectedIcon;
  final Function(String) onIconSelected;

  const IconPicker({
    Key? key,
    this.selectedIcon,
    required this.onIconSelected,
  }) : super(key: key);

  @override
  State<IconPicker> createState() => _IconPickerState();
}

class _IconPickerState extends State<IconPicker> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedIcon;

  // Predefined icon categories
  static const Map<String, List<String>> _iconCategories = {
    'General': [
      '📦', '📋', '📊', '📈', '📉', '💼', '🎯', '⭐', '🔥', '💡',
      '🎨', '🔧', '⚙️', '🎪', '🎭', '🎨', '🎵', '🎸', '🎤', '🎧',
    ],
    'Food': [
      '🍽️', '🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🍖',
      '🍗', '🥓', '🍳', '🥞', '🧇', '🥐', '🍞', '🥖', '🥨', '🧀',
      '🥗', '🍲', '🍱', '🍘', '🍙', '🍚', '🍛', '🍜', '🍝', '🍠',
      '🍢', '🍣', '🍤', '🍥', '🥮', '🍡', '🥟', '🥠', '🥡', '🦀',
      '🦞', '🦐', '🦑', '🐙', '🍇', '🍈', '🍉', '🍊', '🍋', '🍌',
      '🍍', '🥭', '🍎', '🍏', '🍐', '🍑', '🍒', '🍓', '🫐', '🥝',
      '🍅', '🫒', '🥥', '🥑', '🍆', '🥔', '🥕', '🌽', '🌶️', '🫑',
      '🥒', '🥬', '🥦', '🧄', '🧅', '🍄', '🥜', '🌰', '☕', '🍵',
      '🧃', '🥤', '🧋', '🍶', '🍾', '🍷', '🍸', '🍹', '🍺', '🍻',
    ],
    'Transport': [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼',
      '🚁', '🛸', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛰️',
      '🚢', '⛵', '🛶', '🚤', '🛥️', '🛳️', '⛴️', '🚂', '🚃', '🚄',
      '🚅', '🚆', '🚇', '🚈', '🚉', '🚊', '🚝', '🚞', '🚋', '🚌',
      '🚍', '🚎', '🚐', '🚑', '🚒', '🚓', '🚔', '🚕', '🚖', '🚗',
    ],
    'Shopping': [
      '🛍️', '🛒', '🏪', '🏬', '🛵', '💳', '💰', '💸', '💵', '💴',
      '💶', '💷', '🪙', '💎', '⚖️', '🧾', '🏷️', '📦', '📋', '🎁',
      '👕', '👖', '👗', '👚', '👔', '👞', '👟', '👠', '👡', '👢',
      '👑', '👒', '🎩', '🧢', '⛑️', '📿', '💄', '💍', '💎', '🔍',
    ],
    'Entertainment': [
      '🎬', '🎭', '🎪', '🎨', '🎰', '🎲', '🎯', '🎳', '🎮', '🕹️',
      '🎻', '🎸', '🎺', '🎷', '🥁', '🎹', '🎤', '🎧', '📻', '📺',
      '📽️', '🎥', '📷', '📸', '📹', '📼', '💿', '💾', '💽', '💻',
      '🖥️', '🖨️', '⌨️', '🖱️', '🖲️', '💡', '🔦', '🕯️', '🪔', '🔥',
    ],
    'Health': [
      '🏥', '⚕️', '🩺', '💊', '💉', '🩹', '🩼', '🦽', '🦼', '🩻',
      '🧬', '🦠', '💗', '💓', '💕', '💖', '💘', '💝', '💟', '❤️',
      '🧡', '💛', '💚', '💙', '💜', '🤍', '🖤', '🤎', '💔', '❣️',
      '💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💣', '💬', '👁️',
    ],
    'Money': [
      '💰', '💸', '💵', '💴', '💶', '💷', '🪙', '💳', '🏧', '💎',
      '⚖️', '🧾', '📊', '📈', '📉', '💹', '💲', '💱', '🏦', '🏪',
      '🏬', '🛒', '🛍️', '🎰', '🎲', '🃏', '🀄', '🎴', '🎯', '🎪',
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _iconCategories.length, vsync: this);
    _selectedIcon = widget.selectedIcon;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    'Choose Icon',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (_selectedIcon != null) ...[
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _selectedIcon!,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: _iconCategories.keys.map((category) {
                return Tab(text: category);
              }).toList(),
            ),

            // Icon grid
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: _iconCategories.entries.map((entry) {
                  return GridView.builder(
                    padding: const EdgeInsets.all(16),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 6,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: entry.value.length,
                    itemBuilder: (context, index) {
                      final icon = entry.value[index];
                      final isSelected = _selectedIcon == icon;
                      
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedIcon = icon;
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? Theme.of(context).primaryColor.withOpacity(0.2)
                                : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).primaryColor
                                  : Colors.transparent,
                              width: 2,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              icon,
                              style: const TextStyle(fontSize: 24),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }).toList(),
              ),
            ),

            // Action buttons
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedIcon != null
                          ? () {
                              widget.onIconSelected(_selectedIcon!);
                              Navigator.of(context).pop();
                            }
                          : null,
                      child: const Text('Select'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Helper function to show icon picker
Future<String?> showIconPicker(BuildContext context, {String? selectedIcon}) {
  return showDialog<String>(
    context: context,
    builder: (context) => IconPicker(
      selectedIcon: selectedIcon,
      onIconSelected: (icon) => Navigator.of(context).pop(icon),
    ),
  );
}
