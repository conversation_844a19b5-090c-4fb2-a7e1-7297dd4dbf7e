import '../models/monthly_report.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../services/database_service.dart';
import '../services/budget_service.dart';

class MonthlyReportService {
  static final MonthlyReportService _instance = MonthlyReportService._internal();
  factory MonthlyReportService() => _instance;
  MonthlyReportService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final BudgetService _budgetService = BudgetService();

  /// Generate monthly report for specific month and year
  Future<MonthlyReport> generateMonthlyReport(int year, int month) async {
    // Get all transactions for the month
    final transactions = await _getTransactionsForMonth(year, month);
    
    // Calculate basic metrics
    final expenseTransactions = transactions.where((t) => t.type == TransactionType.expense).toList();
    final incomeTransactions = transactions.where((t) => t.type == TransactionType.income).toList();
    
    final totalExpenses = expenseTransactions.fold(0.0, (sum, t) => sum + t.amount);
    final totalIncome = incomeTransactions.fold(0.0, (sum, t) => sum + t.amount);
    
    // Group by categories
    final expensesByCategory = <String, double>{};
    final incomeByCategory = <String, double>{};
    
    for (final transaction in expenseTransactions) {
      expensesByCategory[transaction.category] = 
          (expensesByCategory[transaction.category] ?? 0.0) + transaction.amount;
    }
    
    for (final transaction in incomeTransactions) {
      incomeByCategory[transaction.category] = 
          (incomeByCategory[transaction.category] ?? 0.0) + transaction.amount;
    }
    
    // Generate daily transactions summary
    final dailyTransactions = _generateDailyTransactions(transactions, year, month);
    
    // Get budget comparisons
    final budgetComparisons = await _getBudgetComparisons(year, month, expensesByCategory);
    
    return MonthlyReport(
      year: year,
      month: month,
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      expensesByCategory: expensesByCategory,
      incomeByCategory: incomeByCategory,
      dailyTransactions: dailyTransactions,
      totalTransactionCount: transactions.length,
      budgetComparisons: budgetComparisons,
    );
  }

  /// Get available months with transactions
  Future<List<MonthYear>> getAvailableMonths() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('''
      SELECT DISTINCT 
        strftime('%Y', datetime(date/1000, 'unixepoch')) as year,
        strftime('%m', datetime(date/1000, 'unixepoch')) as month
      FROM transactions 
      ORDER BY year DESC, month DESC
    ''');
    
    return result.map((row) => MonthYear(
      year: int.parse(row['year'] as String),
      month: int.parse(row['month'] as String),
    )).toList();
  }

  /// Generate comparison between current and previous months
  Future<MonthlyComparison> generateMonthlyComparison(int year, int month) async {
    final currentMonth = await generateMonthlyReport(year, month);
    
    // Get previous month
    MonthlyReport? previousMonth;
    final prevMonth = month == 1 ? 12 : month - 1;
    final prevYear = month == 1 ? year - 1 : year;
    
    try {
      previousMonth = await generateMonthlyReport(prevYear, prevMonth);
    } catch (e) {
      // Previous month data might not exist
      previousMonth = null;
    }
    
    // Get same month last year
    MonthlyReport? sameMonthLastYear;
    try {
      sameMonthLastYear = await generateMonthlyReport(year - 1, month);
    } catch (e) {
      // Last year data might not exist
      sameMonthLastYear = null;
    }
    
    return MonthlyComparison(
      currentMonth: currentMonth,
      previousMonth: previousMonth,
      sameMonthLastYear: sameMonthLastYear,
    );
  }

  /// Get yearly summary (all months in a year)
  Future<List<MonthlyReport>> getYearlyReports(int year) async {
    final reports = <MonthlyReport>[];
    
    for (int month = 1; month <= 12; month++) {
      try {
        final report = await generateMonthlyReport(year, month);
        if (report.totalTransactionCount > 0) {
          reports.add(report);
        }
      } catch (e) {
        // Skip months with no data
        continue;
      }
    }
    
    return reports;
  }

  /// Get transactions for specific month
  Future<List<Transaction>> _getTransactionsForMonth(int year, int month) async {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 1).subtract(const Duration(days: 1));
    
    final db = await _databaseService.database;
    final maps = await db.query(
      'transactions',
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'date ASC',
    );
    
    return maps.map((map) => Transaction.fromMap(map)).toList();
  }

  /// Generate daily transaction summaries
  List<DailyTransaction> _generateDailyTransactions(
    List<Transaction> transactions, 
    int year, 
    int month,
  ) {
    final daysInMonth = DateTime(year, month + 1, 0).day;
    final dailyTransactions = <DailyTransaction>[];
    
    for (int day = 1; day <= daysInMonth; day++) {
      final dayTransactions = transactions.where((t) => t.date.day == day).toList();
      
      final dayExpenses = dayTransactions
          .where((t) => t.type == TransactionType.expense)
          .fold(0.0, (sum, t) => sum + t.amount);
      
      final dayIncome = dayTransactions
          .where((t) => t.type == TransactionType.income)
          .fold(0.0, (sum, t) => sum + t.amount);
      
      dailyTransactions.add(DailyTransaction(
        day: day,
        totalIncome: dayIncome,
        totalExpenses: dayExpenses,
        transactionCount: dayTransactions.length,
      ));
    }
    
    return dailyTransactions;
  }

  /// Get budget comparisons for the month
  Future<List<BudgetComparison>> _getBudgetComparisons(
    int year, 
    int month, 
    Map<String, double> expensesByCategory,
  ) async {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 1).subtract(const Duration(days: 1));
    
    // Get budgets that were active during this month
    final db = await _databaseService.database;
    final budgetMaps = await db.query(
      'budgets',
      where: 'isActive = 1 AND startDate <= ? AND endDate >= ?',
      whereArgs: [
        endDate.millisecondsSinceEpoch,
        startDate.millisecondsSinceEpoch,
      ],
    );
    
    final budgetComparisons = <BudgetComparison>[];
    
    for (final budgetMap in budgetMaps) {
      final budget = Budget.fromMap(budgetMap);
      final actualAmount = expensesByCategory[budget.categoryName] ?? 0.0;
      
      // Calculate prorated budget amount for the month
      final budgetStartInMonth = budget.startDate.isBefore(startDate) ? startDate : budget.startDate;
      final budgetEndInMonth = budget.endDate.isAfter(endDate) ? endDate : budget.endDate;
      
      final daysInBudgetPeriod = budget.endDate.difference(budget.startDate).inDays + 1;
      final daysInMonth = budgetEndInMonth.difference(budgetStartInMonth).inDays + 1;
      
      final proratedBudgetAmount = daysInBudgetPeriod > 0 
          ? (budget.budgetAmount * daysInMonth) / daysInBudgetPeriod
          : budget.budgetAmount;
      
      budgetComparisons.add(BudgetComparison(
        categoryName: budget.categoryName,
        budgetAmount: proratedBudgetAmount,
        actualAmount: actualAmount,
      ));
    }
    
    return budgetComparisons;
  }

  /// Get spending trends over multiple months
  Future<List<MonthlyTrend>> getSpendingTrends(int months) async {
    final trends = <MonthlyTrend>[];
    final now = DateTime.now();
    
    for (int i = 0; i < months; i++) {
      final targetDate = DateTime(now.year, now.month - i, 1);
      try {
        final report = await generateMonthlyReport(targetDate.year, targetDate.month);
        trends.add(MonthlyTrend(
          year: targetDate.year,
          month: targetDate.month,
          totalExpenses: report.totalExpenses,
          totalIncome: report.totalIncome,
          netSavings: report.netSavings,
        ));
      } catch (e) {
        // Skip months with no data
        continue;
      }
    }
    
    return trends.reversed.toList(); // Return in chronological order
  }

  /// Export monthly report data
  Map<String, dynamic> exportMonthlyReport(MonthlyReport report) {
    return {
      'month': report.displayTitle,
      'summary': {
        'totalIncome': report.totalIncome,
        'totalExpenses': report.totalExpenses,
        'netSavings': report.netSavings,
        'savingsRate': report.savingsRate,
        'transactionCount': report.totalTransactionCount,
      },
      'categories': {
        'expenses': report.expensesByCategory,
        'income': report.incomeByCategory,
      },
      'dailyData': report.dailyTransactions.map((d) => {
        'day': d.day,
        'income': d.totalIncome,
        'expenses': d.totalExpenses,
        'net': d.netAmount,
      }).toList(),
      'budgetComparisons': report.budgetComparisons.map((b) => {
        'category': b.categoryName,
        'budget': b.budgetAmount,
        'actual': b.actualAmount,
        'utilization': b.utilizationPercentage,
        'overBudget': b.isOverBudget,
      }).toList(),
    };
  }
}

class MonthYear {
  final int year;
  final int month;

  MonthYear({required this.year, required this.month});

  String get displayName {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[month - 1]} $year';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MonthYear && runtimeType == other.runtimeType &&
      year == other.year && month == other.month;

  @override
  int get hashCode => year.hashCode ^ month.hashCode;
}

class MonthlyTrend {
  final int year;
  final int month;
  final double totalExpenses;
  final double totalIncome;
  final double netSavings;

  MonthlyTrend({
    required this.year,
    required this.month,
    required this.totalExpenses,
    required this.totalIncome,
    required this.netSavings,
  });

  String get monthName {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }
}
