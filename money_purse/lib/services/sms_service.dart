import '../services/permission_service.dart';

// Mock SMS message class for demonstration
class SmsMessage {
  final String? address;
  final String? body;
  final int? date;

  SmsMessage({this.address, this.body, this.date});
}

class SmsService {
  static final SmsService _instance = SmsService._internal();
  factory SmsService() => _instance;
  SmsService._internal();

  final PermissionService _permissionService = PermissionService();
  // SMS query functionality to be implemented with proper SMS package

  /// Known bank and payment service senders
  static const List<String> _bankSenders = [
    // Indian Banks
    'SBIINB', 'HDFCBK', 'ICICIB', 'AXISBK', 'KOTAKB', 'PNBSMS', 'BOBSMS',
    'CANBNK', 'UNIBKS', 'IDBIBK', 'YESBNK', 'INDBK', 'ALHABK', 'KVBSMS',

    // Payment Services
    'PAYTM', 'GPAY', 'PHONEPE', 'MOBIKW', 'FREECHAR<PERSON>', 'AMA<PERSON>ON<PERSON>', 'FLIPKRT',
    'OLACA<PERSON>', 'UBE<PERSON>', 'SWIGGY', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'MYNT<PERSON>', '<PERSON>IO',

    // Credit Cards
    'HDFCCC', 'ICICIC', 'SBICC', 'AXISCC', 'KOTAKC', 'AMEXIN', 'CITIBK',

    // Generic patterns
    'ALERTS', 'NOTIFY', 'INFO', 'UPDATE', 'BANK', 'CARD', 'PAY', 'TXN',
  ];

  /// Transaction keywords to identify financial messages
  static const List<String> _transactionKeywords = [
    // Transaction types
    'debited', 'credited', 'paid', 'received', 'transferred', 'withdrawn',
    'deposit', 'purchase', 'refund', 'cashback', 'reward',

    // Currency symbols and terms
    'rs', 'inr', '₹', 'rupees', 'amount', 'balance', 'total',

    // Transaction terms
    'transaction', 'txn', 'payment', 'bill', 'recharge', 'transfer',
    'upi', 'card', 'atm', 'pos', 'online', 'mobile',

    // Account terms
    'account', 'acc', 'savings', 'current', 'wallet', 'bank',
  ];

  /// Read all SMS messages and filter for potential transactions
  Future<List<SmsMessage>> getTransactionSms({
    int? count,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // Check permission first
    if (!await _permissionService.isSmsPermissionGranted()) {
      throw Exception('SMS permission not granted');
    }

    try {
      // TODO: Implement actual SMS reading with proper SMS package
      // For now, return mock data for demonstration
      List<SmsMessage> messages = _getMockTransactionMessages();

      // Filter for transaction-related messages
      return messages.where(_isTransactionMessage).toList();
    } catch (e) {
      throw Exception('Failed to read SMS messages: $e');
    }
  }

  /// Check if an SMS message is likely a transaction notification
  bool _isTransactionMessage(SmsMessage message) {
    final sender = message.address?.toUpperCase() ?? '';
    final body = message.body?.toLowerCase() ?? '';

    // Check if sender is a known bank/payment service
    bool isFromKnownSender = _bankSenders.any(
      (bankSender) => sender.contains(bankSender.toUpperCase()),
    );

    // Check if message contains transaction keywords
    bool hasTransactionKeywords = _transactionKeywords.any(
      (keyword) => body.contains(keyword.toLowerCase()),
    );

    // Check for currency amounts (₹ or Rs followed by numbers)
    bool hasAmount = RegExp(
      r'(₹|rs\.?|inr)\s*\d+',
      caseSensitive: false,
    ).hasMatch(body);

    // Message is likely a transaction if it meets any of these criteria:
    // 1. From known sender AND has transaction keywords
    // 2. From known sender AND has amount
    // 3. Has transaction keywords AND has amount (for unknown senders)
    return (isFromKnownSender && hasTransactionKeywords) ||
        (isFromKnownSender && hasAmount) ||
        (hasTransactionKeywords && hasAmount);
  }

  /// Get recent transaction SMS (last 30 days by default)
  Future<List<SmsMessage>> getRecentTransactionSms({int days = 30}) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    return await getTransactionSms(startDate: startDate, endDate: endDate);
  }

  /// Get transaction SMS from a specific date range
  Future<List<SmsMessage>> getTransactionSmsInRange({
    required DateTime startDate,
    required DateTime endDate,
    int? maxCount,
  }) async {
    return await getTransactionSms(
      startDate: startDate,
      endDate: endDate,
      count: maxCount,
    );
  }

  /// Get unprocessed transaction SMS (messages not yet categorized)
  Future<List<SmsMessage>> getUnprocessedTransactionSms({
    required List<String> processedMessageIds,
    int days = 30,
  }) async {
    final allMessages = await getRecentTransactionSms(days: days);

    // Filter out already processed messages
    return allMessages.where((message) {
      final messageId = _generateMessageId(message);
      return !processedMessageIds.contains(messageId);
    }).toList();
  }

  /// Generate a unique ID for an SMS message
  String _generateMessageId(SmsMessage message) {
    // Create ID based on sender, body hash, and timestamp
    final content = '${message.address}_${message.body}_${message.date}';
    return content.hashCode.toString();
  }

  /// Check if SMS reading is available on this platform
  Future<bool> isSmsReadingAvailable() async {
    try {
      // TODO: Implement actual SMS availability check
      return await _permissionService.isSmsPermissionGranted();
    } catch (e) {
      return false;
    }
  }

  /// Generate mock transaction messages for demonstration
  List<SmsMessage> _getMockTransactionMessages() {
    final now = DateTime.now();
    return [
      SmsMessage(
        address: 'HDFCBK',
        body:
            'Dear Customer, Rs.1,250.00 debited from your account ending 1234 at AMAZON on ${now.day}/${now.month}/${now.year}. Available balance: Rs.15,750.00',
        date: now.subtract(const Duration(hours: 2)).millisecondsSinceEpoch,
      ),
      SmsMessage(
        address: 'PAYTM',
        body:
            'Rs.500 credited to your Paytm Wallet from UPI. Transaction ID: T${now.millisecondsSinceEpoch}',
        date: now.subtract(const Duration(days: 1)).millisecondsSinceEpoch,
      ),
      SmsMessage(
        address: 'SBIINB',
        body:
            'SBI: Rs.2,100.00 debited from A/c XX1234 on ${now.day}/${now.month}/${now.year} for UPI/SWIGGY. Available Bal: Rs.8,900.00',
        date: now.subtract(const Duration(days: 2)).millisecondsSinceEpoch,
      ),
      SmsMessage(
        address: 'ICICIB',
        body:
            'ICICI Bank: Rs.75.00 debited from account XX5678 for METRO CARD RECHARGE on ${now.day}/${now.month}/${now.year}. Available balance Rs.12,425.00',
        date: now.subtract(const Duration(days: 3)).millisecondsSinceEpoch,
      ),
      SmsMessage(
        address: 'GPAY',
        body:
            'You received Rs.1,000 from John Doe via Google Pay. UPI Ref ID: ${now.millisecondsSinceEpoch}',
        date: now.subtract(const Duration(days: 4)).millisecondsSinceEpoch,
      ),
    ];
  }

  /// Get SMS statistics
  Future<SmsStats> getSmsStats({int days = 30}) async {
    try {
      final messages = await getRecentTransactionSms(days: days);

      final Map<String, int> senderCounts = {};
      int totalMessages = messages.length;

      for (final message in messages) {
        final sender = message.address ?? 'Unknown';
        senderCounts[sender] = (senderCounts[sender] ?? 0) + 1;
      }

      return SmsStats(
        totalMessages: totalMessages,
        senderCounts: senderCounts,
        dateRange: days,
      );
    } catch (e) {
      return SmsStats(totalMessages: 0, senderCounts: {}, dateRange: days);
    }
  }
}

/// SMS statistics model
class SmsStats {
  final int totalMessages;
  final Map<String, int> senderCounts;
  final int dateRange;

  SmsStats({
    required this.totalMessages,
    required this.senderCounts,
    required this.dateRange,
  });

  List<MapEntry<String, int>> get topSenders {
    final entries = senderCounts.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(10).toList();
  }
}
