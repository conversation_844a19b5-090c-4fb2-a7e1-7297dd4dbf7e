import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/transaction.dart' as app_models;

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;
  static const String _databaseName = 'money_purse.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _transactionsTable = 'transactions';
  static const String _processedSmsTable = 'processed_sms';

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create transactions table
    await db.execute('''
      CREATE TABLE $_transactionsTable (
        id TEXT PRIMARY KEY,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        merchant TEXT,
        date INTEGER NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        subcategory TEXT,
        smsBody TEXT,
        smsAddress TEXT,
        isVerified INTEGER NOT NULL DEFAULT 0,
        createdAt INTEGER NOT NULL
      )
    ''');

    // Create processed SMS table to track which messages have been processed
    await db.execute('''
      CREATE TABLE $_processedSmsTable (
        id TEXT PRIMARY KEY,
        smsAddress TEXT NOT NULL,
        smsBody TEXT NOT NULL,
        smsDate INTEGER NOT NULL,
        processedAt INTEGER NOT NULL,
        transactionId TEXT,
        FOREIGN KEY (transactionId) REFERENCES $_transactionsTable (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute(
      'CREATE INDEX idx_transactions_date ON $_transactionsTable (date)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_type ON $_transactionsTable (type)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_category ON $_transactionsTable (category)',
    );
    await db.execute(
      'CREATE INDEX idx_processed_sms_date ON $_processedSmsTable (smsDate)',
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add upgrade logic for future versions
    }
  }

  // Transaction CRUD operations

  /// Insert a new transaction
  Future<String> insertTransaction(app_models.Transaction transaction) async {
    final db = await database;
    await db.insert(_transactionsTable, transaction.toMap());
    return transaction.id;
  }

  /// Insert multiple transactions
  Future<void> insertTransactions(
    List<app_models.Transaction> transactions,
  ) async {
    final db = await database;
    final batch = db.batch();

    for (final transaction in transactions) {
      batch.insert(_transactionsTable, transaction.toMap());
    }

    await batch.commit();
  }

  /// Get transaction by ID
  Future<app_models.Transaction?> getTransaction(String id) async {
    final db = await database;
    final maps = await db.query(
      _transactionsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return app_models.Transaction.fromMap(maps.first);
    }
    return null;
  }

  /// Get all transactions
  Future<List<app_models.Transaction>> getAllTransactions() async {
    final db = await database;
    final maps = await db.query(_transactionsTable, orderBy: 'date DESC');
    return maps.map((map) => app_models.Transaction.fromMap(map)).toList();
  }

  /// Get transactions by date range
  Future<List<app_models.Transaction>> getTransactionsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await database;
    final maps = await db.query(
      _transactionsTable,
      where: 'date BETWEEN ? AND ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'date DESC',
    );
    return maps.map((map) => app_models.Transaction.fromMap(map)).toList();
  }

  /// Get transactions by type
  Future<List<app_models.Transaction>> getTransactionsByType(
    app_models.TransactionType type,
  ) async {
    final db = await database;
    final maps = await db.query(
      _transactionsTable,
      where: 'type = ?',
      whereArgs: [type.toString()],
      orderBy: 'date DESC',
    );
    return maps.map((map) => app_models.Transaction.fromMap(map)).toList();
  }

  /// Get transactions by category
  Future<List<app_models.Transaction>> getTransactionsByCategory(
    String category,
  ) async {
    final db = await database;
    final maps = await db.query(
      _transactionsTable,
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'date DESC',
    );
    return maps.map((map) => app_models.Transaction.fromMap(map)).toList();
  }

  /// Update transaction
  Future<void> updateTransaction(app_models.Transaction transaction) async {
    final db = await database;
    await db.update(
      _transactionsTable,
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  /// Delete transaction
  Future<void> deleteTransaction(String id) async {
    final db = await database;
    await db.delete(_transactionsTable, where: 'id = ?', whereArgs: [id]);
  }

  /// Get recent transactions (last N days)
  Future<List<app_models.Transaction>> getRecentTransactions({
    int days = 30,
  }) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));
    return await getTransactionsByDateRange(
      startDate: startDate,
      endDate: endDate,
    );
  }

  // Processed SMS tracking

  /// Mark SMS as processed
  Future<void> markSmsAsProcessed({
    required String smsId,
    required String smsAddress,
    required String smsBody,
    required DateTime smsDate,
    String? transactionId,
  }) async {
    final db = await database;
    await db.insert(_processedSmsTable, {
      'id': smsId,
      'smsAddress': smsAddress,
      'smsBody': smsBody,
      'smsDate': smsDate.millisecondsSinceEpoch,
      'processedAt': DateTime.now().millisecondsSinceEpoch,
      'transactionId': transactionId,
    });
  }

  /// Check if SMS has been processed
  Future<bool> isSmsProcessed(String smsId) async {
    final db = await database;
    final maps = await db.query(
      _processedSmsTable,
      where: 'id = ?',
      whereArgs: [smsId],
    );
    return maps.isNotEmpty;
  }

  /// Get all processed SMS IDs
  Future<List<String>> getProcessedSmsIds() async {
    final db = await database;
    final maps = await db.query(_processedSmsTable, columns: ['id']);
    return maps.map((map) => map['id'] as String).toList();
  }

  // Analytics and statistics

  /// Get total expenses for a date range
  Future<double> getTotalExpenses({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(amount) as total FROM $_transactionsTable 
      WHERE type = ? AND date BETWEEN ? AND ?
    ''',
      [
        app_models.TransactionType.expense.toString(),
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
    );

    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total income for a date range
  Future<double> getTotalIncome({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(amount) as total FROM $_transactionsTable 
      WHERE type = ? AND date BETWEEN ? AND ?
    ''',
      [
        app_models.TransactionType.income.toString(),
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
    );

    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get spending by category
  Future<Map<String, double>> getSpendingByCategory({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await database;
    final result = await db.rawQuery(
      '''
      SELECT category, SUM(amount) as total FROM $_transactionsTable 
      WHERE type = ? AND date BETWEEN ? AND ?
      GROUP BY category
      ORDER BY total DESC
    ''',
      [
        app_models.TransactionType.expense.toString(),
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
    );

    final Map<String, double> categorySpending = {};
    for (final row in result) {
      categorySpending[row['category'] as String] = row['total'] as double;
    }

    return categorySpending;
  }

  /// Close database connection
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
