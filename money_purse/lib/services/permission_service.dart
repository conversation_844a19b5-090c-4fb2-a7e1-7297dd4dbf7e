import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Check if SMS permission is granted
  Future<bool> isSmsPermissionGranted() async {
    final status = await Permission.sms.status;
    return status.isGranted;
  }

  /// Request SMS permission
  Future<PermissionStatus> requestSmsPermission() async {
    final status = await Permission.sms.request();
    return status;
  }

  /// Check if SMS permission is permanently denied
  Future<bool> isSmsPermissionPermanentlyDenied() async {
    final status = await Permission.sms.status;
    return status.isPermanentlyDenied;
  }

  /// Open app settings for manual permission grant
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Get detailed permission status information
  Future<PermissionStatusInfo> getSmsPermissionInfo() async {
    final status = await Permission.sms.status;
    
    return PermissionStatusInfo(
      status: status,
      isGranted: status.isGranted,
      isDenied: status.isDenied,
      isPermanentlyDenied: status.isPermanentlyDenied,
      isRestricted: status.isRestricted,
      canRequest: !status.isPermanentlyDenied && !status.isGranted,
    );
  }

  /// Handle the complete permission flow
  Future<PermissionResult> handleSmsPermissionFlow() async {
    try {
      // First check current status
      final currentStatus = await Permission.sms.status;
      
      if (currentStatus.isGranted) {
        return PermissionResult.granted();
      }
      
      if (currentStatus.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied();
      }
      
      // Request permission
      final requestResult = await Permission.sms.request();
      
      switch (requestResult) {
        case PermissionStatus.granted:
          return PermissionResult.granted();
        case PermissionStatus.denied:
          return PermissionResult.denied();
        case PermissionStatus.permanentlyDenied:
          return PermissionResult.permanentlyDenied();
        case PermissionStatus.restricted:
          return PermissionResult.restricted();
        default:
          return PermissionResult.denied();
      }
    } catch (e) {
      return PermissionResult.error(e.toString());
    }
  }
}

/// Detailed permission status information
class PermissionStatusInfo {
  final PermissionStatus status;
  final bool isGranted;
  final bool isDenied;
  final bool isPermanentlyDenied;
  final bool isRestricted;
  final bool canRequest;

  PermissionStatusInfo({
    required this.status,
    required this.isGranted,
    required this.isDenied,
    required this.isPermanentlyDenied,
    required this.isRestricted,
    required this.canRequest,
  });
}

/// Result of permission request flow
class PermissionResult {
  final PermissionResultType type;
  final String? errorMessage;

  PermissionResult._(this.type, [this.errorMessage]);

  factory PermissionResult.granted() => PermissionResult._(PermissionResultType.granted);
  factory PermissionResult.denied() => PermissionResult._(PermissionResultType.denied);
  factory PermissionResult.permanentlyDenied() => PermissionResult._(PermissionResultType.permanentlyDenied);
  factory PermissionResult.restricted() => PermissionResult._(PermissionResultType.restricted);
  factory PermissionResult.error(String message) => PermissionResult._(PermissionResultType.error, message);

  bool get isGranted => type == PermissionResultType.granted;
  bool get isDenied => type == PermissionResultType.denied;
  bool get isPermanentlyDenied => type == PermissionResultType.permanentlyDenied;
  bool get isRestricted => type == PermissionResultType.restricted;
  bool get isError => type == PermissionResultType.error;
  bool get canRetry => isDenied && !isPermanentlyDenied;
  bool get needsManualSettings => isPermanentlyDenied;
}

enum PermissionResultType {
  granted,
  denied,
  permanentlyDenied,
  restricted,
  error,
}
