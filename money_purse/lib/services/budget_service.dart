import 'package:sqflite/sqflite.dart';
import '../models/budget.dart';
import '../models/transaction.dart';
import 'database_service.dart';

class BudgetService {
  static final BudgetService _instance = BudgetService._internal();
  factory BudgetService() => _instance;
  BudgetService._internal();

  final DatabaseService _databaseService = DatabaseService();
  static const String _budgetsTable = 'budgets';

  /// Initialize budget table
  Future<void> initializeBudgetTable() async {
    final db = await _databaseService.database;
    
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $_budgetsTable (
        id TEXT PRIMARY KEY,
        categoryName TEXT NOT NULL,
        budgetAmount REAL NOT NULL,
        spentAmount REAL NOT NULL DEFAULT 0.0,
        period TEXT NOT NULL,
        startDate INTEGER NOT NULL,
        endDate INTEGER NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt INTEGER NOT NULL,
        lastUpdated INTEGER
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX IF NOT EXISTS idx_budgets_category ON $_budgetsTable (categoryName)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_budgets_period ON $_budgetsTable (startDate, endDate)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_budgets_active ON $_budgetsTable (isActive)');
  }

  /// Create a new budget
  Future<String> createBudget(Budget budget) async {
    final db = await _databaseService.database;
    await db.insert(_budgetsTable, budget.toMap());
    return budget.id;
  }

  /// Get budget by ID
  Future<Budget?> getBudget(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      _budgetsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Budget.fromMap(maps.first);
    }
    return null;
  }

  /// Get all budgets
  Future<List<Budget>> getAllBudgets() async {
    final db = await _databaseService.database;
    final maps = await db.query(_budgetsTable, orderBy: 'createdAt DESC');
    return maps.map((map) => Budget.fromMap(map)).toList();
  }

  /// Get active budgets
  Future<List<Budget>> getActiveBudgets() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      _budgetsTable,
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: 'createdAt DESC',
    );
    return maps.map((map) => Budget.fromMap(map)).toList();
  }

  /// Get current budgets (active and within date range)
  Future<List<Budget>> getCurrentBudgets() async {
    final now = DateTime.now();
    final db = await _databaseService.database;
    final maps = await db.query(
      _budgetsTable,
      where: 'isActive = ? AND startDate <= ? AND endDate >= ?',
      whereArgs: [1, now.millisecondsSinceEpoch, now.millisecondsSinceEpoch],
      orderBy: 'createdAt DESC',
    );
    return maps.map((map) => Budget.fromMap(map)).toList();
  }

  /// Get budget for specific category
  Future<Budget?> getBudgetForCategory(String categoryName) async {
    final now = DateTime.now();
    final db = await _databaseService.database;
    final maps = await db.query(
      _budgetsTable,
      where: 'categoryName = ? AND isActive = ? AND startDate <= ? AND endDate >= ?',
      whereArgs: [categoryName, 1, now.millisecondsSinceEpoch, now.millisecondsSinceEpoch],
      orderBy: 'createdAt DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Budget.fromMap(maps.first);
    }
    return null;
  }

  /// Update budget
  Future<void> updateBudget(Budget budget) async {
    final db = await _databaseService.database;
    await db.update(
      _budgetsTable,
      budget.toMap(),
      where: 'id = ?',
      whereArgs: [budget.id],
    );
  }

  /// Update budget spent amount
  Future<void> updateBudgetSpentAmount(String budgetId, double spentAmount) async {
    final db = await _databaseService.database;
    await db.update(
      _budgetsTable,
      {
        'spentAmount': spentAmount,
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [budgetId],
    );
  }

  /// Delete budget
  Future<void> deleteBudget(String id) async {
    final db = await _databaseService.database;
    await db.delete(
      _budgetsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Deactivate budget (soft delete)
  Future<void> deactivateBudget(String id) async {
    final db = await _databaseService.database;
    await db.update(
      _budgetsTable,
      {
        'isActive': 0,
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Calculate and update spent amounts for all active budgets
  Future<void> updateAllBudgetSpentAmounts() async {
    final activeBudgets = await getActiveBudgets();
    
    for (final budget in activeBudgets) {
      final spentAmount = await _calculateSpentAmount(budget);
      if (spentAmount != budget.spentAmount) {
        await updateBudgetSpentAmount(budget.id, spentAmount);
      }
    }
  }

  /// Calculate spent amount for a specific budget
  Future<double> _calculateSpentAmount(Budget budget) async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total FROM transactions 
      WHERE category = ? AND type = ? AND date BETWEEN ? AND ?
    ''', [
      budget.categoryName,
      TransactionType.expense.toString(),
      budget.startDate.millisecondsSinceEpoch,
      budget.endDate.millisecondsSinceEpoch,
    ]);
    
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get budget statistics
  Future<BudgetStatistics> getBudgetStatistics() async {
    final budgets = await getCurrentBudgets();
    
    int totalBudgets = budgets.length;
    int onTrackBudgets = 0;
    int nearLimitBudgets = 0;
    int exceededBudgets = 0;
    double totalBudgetAmount = 0.0;
    double totalSpentAmount = 0.0;

    for (final budget in budgets) {
      totalBudgetAmount += budget.budgetAmount;
      totalSpentAmount += budget.spentAmount;

      switch (budget.status) {
        case BudgetStatus.onTrack:
          onTrackBudgets++;
          break;
        case BudgetStatus.nearLimit:
          nearLimitBudgets++;
          break;
        case BudgetStatus.exceeded:
          exceededBudgets++;
          break;
        default:
          break;
      }
    }

    return BudgetStatistics(
      totalBudgets: totalBudgets,
      onTrackBudgets: onTrackBudgets,
      nearLimitBudgets: nearLimitBudgets,
      exceededBudgets: exceededBudgets,
      totalBudgetAmount: totalBudgetAmount,
      totalSpentAmount: totalSpentAmount,
    );
  }

  /// Get budgets expiring soon (within next 7 days)
  Future<List<Budget>> getBudgetsExpiringSoon() async {
    final now = DateTime.now();
    final weekFromNow = now.add(const Duration(days: 7));
    
    final db = await _databaseService.database;
    final maps = await db.query(
      _budgetsTable,
      where: 'isActive = ? AND endDate BETWEEN ? AND ?',
      whereArgs: [1, now.millisecondsSinceEpoch, weekFromNow.millisecondsSinceEpoch],
      orderBy: 'endDate ASC',
    );
    
    return maps.map((map) => Budget.fromMap(map)).toList();
  }

  /// Create budget for next period (auto-renewal)
  Future<Budget> createNextPeriodBudget(Budget currentBudget) async {
    final nextStartDate = currentBudget.endDate.add(const Duration(days: 1));
    final nextEndDate = _calculateEndDate(nextStartDate, currentBudget.period);
    
    final nextBudget = Budget(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      categoryName: currentBudget.categoryName,
      budgetAmount: currentBudget.budgetAmount,
      period: currentBudget.period,
      startDate: nextStartDate,
      endDate: nextEndDate,
      createdAt: DateTime.now(),
    );
    
    await createBudget(nextBudget);
    return nextBudget;
  }

  /// Calculate end date based on start date and period
  DateTime _calculateEndDate(DateTime startDate, BudgetPeriod period) {
    switch (period) {
      case BudgetPeriod.weekly:
        return startDate.add(const Duration(days: 7));
      case BudgetPeriod.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case BudgetPeriod.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case BudgetPeriod.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
      case BudgetPeriod.custom:
        return startDate.add(const Duration(days: 30)); // Default 30 days
    }
  }
}

/// Budget statistics model
class BudgetStatistics {
  final int totalBudgets;
  final int onTrackBudgets;
  final int nearLimitBudgets;
  final int exceededBudgets;
  final double totalBudgetAmount;
  final double totalSpentAmount;

  BudgetStatistics({
    required this.totalBudgets,
    required this.onTrackBudgets,
    required this.nearLimitBudgets,
    required this.exceededBudgets,
    required this.totalBudgetAmount,
    required this.totalSpentAmount,
  });

  double get totalRemainingAmount => totalBudgetAmount - totalSpentAmount;
  double get overallUsagePercentage => totalBudgetAmount > 0 ? totalSpentAmount / totalBudgetAmount : 0.0;
}
