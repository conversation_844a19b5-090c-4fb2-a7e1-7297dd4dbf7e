import '../models/transaction.dart';
import '../services/sms_service.dart';
import '../utils/string_utils.dart';

class TransactionParser {
  static final TransactionParser _instance = TransactionParser._internal();
  factory TransactionParser() => _instance;
  TransactionParser._internal();

  /// Parse SMS message to extract transaction details
  ParsedTransaction? parseTransaction(SmsMessage smsMessage) {
    final body = smsMessage.body?.toLowerCase() ?? '';
    final sender = smsMessage.address ?? '';
    final date = smsMessage.date != null
        ? DateTime.fromMillisecondsSinceEpoch(smsMessage.date!)
        : DateTime.now();

    try {
      // Extract amount
      final amount = _extractAmount(body);
      if (amount == null) return null;

      // Determine transaction type
      final type = _determineTransactionType(body);

      // Extract merchant/description
      final merchant = _extractMerchant(body, sender);
      final description = _generateDescription(body, merchant, type);

      return ParsedTransaction(
        amount: amount,
        description: description,
        merchant: merchant,
        date: date,
        type: type,
        smsBody: smsMessage.body,
        smsAddress: sender,
        confidence: _calculateConfidence(body, amount, merchant),
      );
    } catch (e) {
      return null;
    }
  }

  /// Extract amount from SMS body using various patterns
  double? _extractAmount(String body) {
    // Common amount patterns
    final patterns = [
      // ₹1,234.56 or Rs.1,234.56 or INR 1,234.56
      RegExp(
        r'(?:₹|rs\.?|inr)\s*([0-9,]+(?:\.[0-9]{2})?)',
        caseSensitive: false,
      ),
      // 1,234.56 followed by currency
      RegExp(
        r'([0-9,]+(?:\.[0-9]{2})?)\s*(?:₹|rs\.?|inr|rupees?)',
        caseSensitive: false,
      ),
      // Amount: 1,234.56
      RegExp(
        r'amount[:\s]*(?:₹|rs\.?|inr)?\s*([0-9,]+(?:\.[0-9]{2})?)',
        caseSensitive: false,
      ),
      // Debited/Credited by 1,234.56
      RegExp(
        r'(?:debited|credited|paid|received)\s+(?:by|of|for)?\s*(?:₹|rs\.?|inr)?\s*([0-9,]+(?:\.[0-9]{2})?)',
        caseSensitive: false,
      ),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(body);
      if (match != null) {
        final amountStr = match.group(1)?.replaceAll(',', '') ?? '';
        final amount = double.tryParse(amountStr);
        if (amount != null && amount > 0) {
          return amount;
        }
      }
    }

    return null;
  }

  /// Determine if transaction is expense, income, or transfer
  TransactionType _determineTransactionType(String body) {
    // Income indicators
    final incomeKeywords = [
      'credited',
      'received',
      'deposit',
      'salary',
      'refund',
      'cashback',
      'reward',
      'bonus',
      'interest',
      'dividend',
      'transfer received',
    ];

    // Expense indicators
    final expenseKeywords = [
      'debited',
      'paid',
      'purchase',
      'withdrawn',
      'spent',
      'bill',
      'recharge',
      'transfer sent',
      'fee',
      'charge',
    ];

    // Check for income keywords
    for (final keyword in incomeKeywords) {
      if (body.contains(keyword)) {
        return TransactionType.income;
      }
    }

    // Check for expense keywords
    for (final keyword in expenseKeywords) {
      if (body.contains(keyword)) {
        return TransactionType.expense;
      }
    }

    // Default to expense if unclear
    return TransactionType.expense;
  }

  /// Extract merchant name from SMS body
  String? _extractMerchant(String body, String sender) {
    // Try to extract merchant from common patterns
    final patterns = [
      // "at MERCHANT NAME"
      RegExp(r'at\s+([A-Z][A-Z0-9\s&.-]{2,30})', caseSensitive: false),
      // "to MERCHANT NAME"
      RegExp(r'to\s+([A-Z][A-Z0-9\s&.-]{2,30})', caseSensitive: false),
      // "from MERCHANT NAME"
      RegExp(r'from\s+([A-Z][A-Z0-9\s&.-]{2,30})', caseSensitive: false),
      // "MERCHANT NAME" (after amount)
      RegExp(
        r'(?:₹|rs\.?|inr)\s*[0-9,]+(?:\.[0-9]{2})?\s+(?:at|to|from)?\s*([A-Z][A-Z0-9\s&.-]{2,30})',
        caseSensitive: false,
      ),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(body);
      if (match != null) {
        final merchant = match.group(1)?.trim();
        if (merchant != null && merchant.length > 2) {
          return StringUtils.toTitleCase(merchant);
        }
      }
    }

    // Try to extract from UPI patterns
    final upiPattern = RegExp(
      r'upi[/\s]*(?:id[:\s]*)?([a-zA-Z0-9@.-]+)',
      caseSensitive: false,
    );
    final upiMatch = upiPattern.firstMatch(body);
    if (upiMatch != null) {
      final upiId = upiMatch.group(1);
      if (upiId != null) {
        // Extract merchant name from UPI ID (before @)
        final merchantFromUpi = upiId.split('@').first;
        return StringUtils.toTitleCase(merchantFromUpi.replaceAll('.', ' '));
      }
    }

    // Fallback to sender if no merchant found
    return _cleanSenderName(sender);
  }

  /// Clean and format sender name
  String? _cleanSenderName(String sender) {
    if (sender.isEmpty) return null;

    // Remove common prefixes/suffixes
    String cleaned = sender
        .replaceAll(RegExp(r'^(VM-|AD-|BP-|DM-|TM-)'), '')
        .replaceAll(RegExp(r'(SMS|ALERT|INFO|NOTIFY)$'), '')
        .trim();

    if (cleaned.length < 3) return null;

    return StringUtils.toTitleCase(cleaned);
  }

  /// Generate transaction description
  String _generateDescription(
    String body,
    String? merchant,
    TransactionType type,
  ) {
    // Try to extract specific transaction descriptions
    final patterns = [
      // Bill payment patterns
      RegExp(
        r'(bill payment|recharge|top-?up)\s+(?:for|to)?\s*([a-zA-Z0-9\s]+)',
        caseSensitive: false,
      ),
      // Purchase patterns
      RegExp(
        r'(purchase|payment|transaction)\s+(?:at|to|from)?\s*([a-zA-Z0-9\s]+)',
        caseSensitive: false,
      ),
      // Transfer patterns
      RegExp(
        r'(transfer|sent|received)\s+(?:to|from)?\s*([a-zA-Z0-9\s]+)',
        caseSensitive: false,
      ),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(body);
      if (match != null) {
        final action = match.group(1) ?? '';
        final target = match.group(2)?.trim() ?? '';
        if (target.isNotEmpty) {
          return '${StringUtils.toTitleCase(action)} - ${StringUtils.toTitleCase(target)}';
        }
      }
    }

    // Fallback descriptions based on type and merchant
    if (merchant != null) {
      switch (type) {
        case TransactionType.expense:
          return 'Payment to $merchant';
        case TransactionType.income:
          return 'Payment from $merchant';
        case TransactionType.ignored:
          return 'Transaction with $merchant';
      }
    }

    // Generic descriptions
    switch (type) {
      case TransactionType.expense:
        return 'Expense Transaction';
      case TransactionType.income:
        return 'Income Transaction';
      case TransactionType.ignored:
        return 'Transaction';
    }
  }

  /// Calculate confidence score for parsed transaction (0.0 to 1.0)
  double _calculateConfidence(String body, double? amount, String? merchant) {
    double confidence = 0.0;

    // Amount found
    if (amount != null && amount > 0) {
      confidence += 0.4;
    }

    // Merchant found
    if (merchant != null && merchant.isNotEmpty) {
      confidence += 0.3;
    }

    // Contains clear transaction keywords
    final strongKeywords = [
      'debited',
      'credited',
      'paid',
      'received',
      'purchase',
    ];
    if (strongKeywords.any((keyword) => body.contains(keyword))) {
      confidence += 0.2;
    }

    // Contains currency symbols
    if (body.contains('₹') || body.contains('rs') || body.contains('inr')) {
      confidence += 0.1;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// Parse multiple SMS messages
  List<ParsedTransaction> parseMultipleTransactions(List<SmsMessage> messages) {
    final transactions = <ParsedTransaction>[];

    for (final message in messages) {
      final parsed = parseTransaction(message);
      if (parsed != null) {
        transactions.add(parsed);
      }
    }

    return transactions;
  }
}

/// Parsed transaction data from SMS
class ParsedTransaction {
  final double amount;
  final String description;
  final String? merchant;
  final DateTime date;
  final TransactionType type;
  final String? smsBody;
  final String? smsAddress;
  final double confidence;

  ParsedTransaction({
    required this.amount,
    required this.description,
    this.merchant,
    required this.date,
    required this.type,
    this.smsBody,
    this.smsAddress,
    required this.confidence,
  });

  /// Convert to Transaction model
  Transaction toTransaction({
    required String id,
    String category = 'Uncategorized',
    String? subcategory,
  }) {
    return Transaction(
      id: id,
      amount: amount,
      description: description,
      merchant: merchant,
      date: date,
      type: type,
      category: category,
      subcategory: subcategory,
      smsBody: smsBody,
      smsAddress: smsAddress,
      isVerified: false,
      createdAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ParsedTransaction(amount: $amount, description: $description, type: $type, confidence: $confidence)';
  }
}
