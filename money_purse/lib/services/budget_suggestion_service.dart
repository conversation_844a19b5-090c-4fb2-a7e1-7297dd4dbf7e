import '../models/budget.dart';
import '../models/monthly_report.dart';
import '../services/monthly_report_service.dart';
import '../services/budget_service.dart';

class BudgetSuggestionService {
  static final BudgetSuggestionService _instance = BudgetSuggestionService._internal();
  factory BudgetSuggestionService() => _instance;
  BudgetSuggestionService._internal();

  final MonthlyReportService _reportService = MonthlyReportService();
  final BudgetService _budgetService = BudgetService();

  /// Generate budget suggestions based on historical data
  Future<BudgetSuggestionPlan> generateBudgetSuggestions({
    int analysisMonths = 3,
    double conservativeBuffer = 0.1, // 10% buffer for conservative approach
    double aggressiveReduction = 0.15, // 15% reduction for aggressive savings
  }) async {
    try {
      // Get historical reports
      final historicalReports = await _getHistoricalReports(analysisMonths);
      
      if (historicalReports.isEmpty) {
        throw Exception('No historical data available for analysis');
      }

      // Analyze spending patterns
      final spendingAnalysis = _analyzeSpendingPatterns(historicalReports);
      
      // Get current budgets for comparison
      final currentBudgets = await _budgetService.getCurrentBudgets();
      
      // Generate different budget approaches
      final conservativePlan = _generateConservativePlan(spendingAnalysis, conservativeBuffer);
      final balancedPlan = _generateBalancedPlan(spendingAnalysis);
      final aggressivePlan = _generateAggressivePlan(spendingAnalysis, aggressiveReduction);
      
      // Generate insights and recommendations
      final insights = _generateInsights(spendingAnalysis, historicalReports);
      
      return BudgetSuggestionPlan(
        analysisMonths: analysisMonths,
        totalHistoricalIncome: spendingAnalysis.averageIncome,
        totalHistoricalExpenses: spendingAnalysis.averageExpenses,
        conservativePlan: conservativePlan,
        balancedPlan: balancedPlan,
        aggressivePlan: aggressivePlan,
        categoryAnalysis: spendingAnalysis.categoryAnalysis,
        insights: insights,
        currentBudgets: currentBudgets,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to generate budget suggestions: $e');
    }
  }

  /// Get historical monthly reports for analysis
  Future<List<MonthlyReport>> _getHistoricalReports(int months) async {
    final reports = <MonthlyReport>[];
    final now = DateTime.now();
    
    for (int i = 1; i <= months; i++) {
      final targetDate = DateTime(now.year, now.month - i, 1);
      try {
        final report = await _reportService.generateMonthlyReport(
          targetDate.year, 
          targetDate.month,
        );
        if (report.totalTransactionCount > 0) {
          reports.add(report);
        }
      } catch (e) {
        // Skip months with no data
        continue;
      }
    }
    
    return reports;
  }

  /// Analyze spending patterns from historical data
  SpendingAnalysis _analyzeSpendingPatterns(List<MonthlyReport> reports) {
    final categoryTotals = <String, List<double>>{};
    double totalIncome = 0;
    double totalExpenses = 0;
    
    // Aggregate data from all reports
    for (final report in reports) {
      totalIncome += report.totalIncome;
      totalExpenses += report.totalExpenses;
      
      // Collect category spending
      for (final entry in report.expensesByCategory.entries) {
        categoryTotals.putIfAbsent(entry.key, () => []).add(entry.value);
      }
    }
    
    // Calculate averages and patterns
    final categoryAnalysis = <String, CategorySpendingPattern>{};
    
    for (final entry in categoryTotals.entries) {
      final amounts = entry.value;
      final average = amounts.reduce((a, b) => a + b) / amounts.length;
      final max = amounts.reduce((a, b) => a > b ? a : b);
      final min = amounts.reduce((a, b) => a < b ? a : b);
      
      // Calculate volatility (standard deviation)
      final variance = amounts.map((x) => (x - average) * (x - average)).reduce((a, b) => a + b) / amounts.length;
      final volatility = variance > 0 ? (variance).sqrt() : 0.0;
      
      // Determine trend
      final trend = _calculateTrend(amounts);
      
      categoryAnalysis[entry.key] = CategorySpendingPattern(
        categoryName: entry.key,
        averageAmount: average,
        maxAmount: max,
        minAmount: min,
        volatility: volatility,
        trend: trend,
        consistency: _calculateConsistency(amounts),
        priority: _calculatePriority(entry.key, average, volatility),
      );
    }
    
    return SpendingAnalysis(
      averageIncome: totalIncome / reports.length,
      averageExpenses: totalExpenses / reports.length,
      categoryAnalysis: categoryAnalysis,
      analysisMonths: reports.length,
    );
  }

  /// Generate conservative budget plan (higher amounts with buffer)
  List<BudgetSuggestion> _generateConservativePlan(SpendingAnalysis analysis, double buffer) {
    final suggestions = <BudgetSuggestion>[];
    
    for (final pattern in analysis.categoryAnalysis.values) {
      // Use max amount + buffer for conservative approach
      final suggestedAmount = pattern.maxAmount * (1 + buffer);
      
      suggestions.add(BudgetSuggestion(
        categoryName: pattern.categoryName,
        suggestedAmount: suggestedAmount,
        reasoning: _getConservativeReasoning(pattern, buffer),
        confidence: _calculateConfidence(pattern, BudgetApproach.conservative),
        priority: pattern.priority,
      ));
    }
    
    return suggestions..sort((a, b) => b.priority.compareTo(a.priority));
  }

  /// Generate balanced budget plan (based on averages with adjustments)
  List<BudgetSuggestion> _generateBalancedPlan(SpendingAnalysis analysis) {
    final suggestions = <BudgetSuggestion>[];
    
    for (final pattern in analysis.categoryAnalysis.values) {
      // Use average + volatility adjustment for balanced approach
      final volatilityAdjustment = pattern.volatility * 0.5;
      final suggestedAmount = pattern.averageAmount + volatilityAdjustment;
      
      suggestions.add(BudgetSuggestion(
        categoryName: pattern.categoryName,
        suggestedAmount: suggestedAmount,
        reasoning: _getBalancedReasoning(pattern),
        confidence: _calculateConfidence(pattern, BudgetApproach.balanced),
        priority: pattern.priority,
      ));
    }
    
    return suggestions..sort((a, b) => b.priority.compareTo(a.priority));
  }

  /// Generate aggressive budget plan (reduced amounts for savings)
  List<BudgetSuggestion> _generateAggressivePlan(SpendingAnalysis analysis, double reduction) {
    final suggestions = <BudgetSuggestion>[];
    
    for (final pattern in analysis.categoryAnalysis.values) {
      // Use minimum or reduced average for aggressive savings
      final baseAmount = pattern.trend == SpendingTrend.decreasing 
          ? pattern.minAmount 
          : pattern.averageAmount;
      final suggestedAmount = baseAmount * (1 - reduction);
      
      suggestions.add(BudgetSuggestion(
        categoryName: pattern.categoryName,
        suggestedAmount: suggestedAmount.clamp(pattern.minAmount * 0.8, pattern.maxAmount),
        reasoning: _getAggressiveReasoning(pattern, reduction),
        confidence: _calculateConfidence(pattern, BudgetApproach.aggressive),
        priority: pattern.priority,
      ));
    }
    
    return suggestions..sort((a, b) => b.priority.compareTo(a.priority));
  }

  /// Generate insights and recommendations
  List<BudgetInsight> _generateInsights(SpendingAnalysis analysis, List<MonthlyReport> reports) {
    final insights = <BudgetInsight>[];
    
    // Overall spending insights
    final avgSavingsRate = reports.map((r) => r.savingsRate).reduce((a, b) => a + b) / reports.length;
    
    if (avgSavingsRate < 10) {
      insights.add(BudgetInsight(
        type: InsightType.warning,
        title: 'Low Savings Rate',
        description: 'Your average savings rate is ${avgSavingsRate.toStringAsFixed(1)}%. Consider the aggressive plan to improve savings.',
        actionable: true,
        priority: InsightPriority.high,
      ));
    } else if (avgSavingsRate > 30) {
      insights.add(BudgetInsight(
        type: InsightType.positive,
        title: 'Excellent Savings Rate',
        description: 'Your ${avgSavingsRate.toStringAsFixed(1)}% savings rate is excellent! You might consider the conservative plan.',
        actionable: false,
        priority: InsightPriority.low,
      ));
    }
    
    // Category-specific insights
    for (final pattern in analysis.categoryAnalysis.values) {
      if (pattern.volatility > pattern.averageAmount * 0.5) {
        insights.add(BudgetInsight(
          type: InsightType.warning,
          title: 'High Spending Volatility',
          description: '${pattern.categoryName} spending varies significantly. Consider the conservative approach for this category.',
          actionable: true,
          priority: InsightPriority.medium,
        ));
      }
      
      if (pattern.trend == SpendingTrend.increasing) {
        insights.add(BudgetInsight(
          type: InsightType.info,
          title: 'Increasing Spending Trend',
          description: '${pattern.categoryName} spending is trending upward. Monitor this category closely.',
          actionable: true,
          priority: InsightPriority.medium,
        ));
      }
    }
    
    return insights..sort((a, b) => b.priority.index.compareTo(a.priority.index));
  }

  // Helper methods for calculations and reasoning
  SpendingTrend _calculateTrend(List<double> amounts) {
    if (amounts.length < 2) return SpendingTrend.stable;
    
    final first = amounts.first;
    final last = amounts.last;
    final change = (last - first) / first;
    
    if (change > 0.1) return SpendingTrend.increasing;
    if (change < -0.1) return SpendingTrend.decreasing;
    return SpendingTrend.stable;
  }

  double _calculateConsistency(List<double> amounts) {
    if (amounts.isEmpty) return 0.0;
    final average = amounts.reduce((a, b) => a + b) / amounts.length;
    final variance = amounts.map((x) => (x - average) * (x - average)).reduce((a, b) => a + b) / amounts.length;
    return average > 0 ? 1 - (variance.sqrt() / average) : 0.0;
  }

  double _calculatePriority(String categoryName, double averageAmount, double volatility) {
    // Essential categories get higher priority
    const essentialCategories = ['Food & Dining', 'Bills & Utilities', 'Healthcare', 'Transportation'];
    double basePriority = essentialCategories.contains(categoryName) ? 0.8 : 0.5;
    
    // Higher amounts get higher priority
    double amountFactor = (averageAmount / 10000).clamp(0.0, 0.3);
    
    // Lower volatility gets higher priority (more predictable)
    double volatilityFactor = (1 - (volatility / averageAmount).clamp(0.0, 1.0)) * 0.2;
    
    return (basePriority + amountFactor + volatilityFactor).clamp(0.0, 1.0);
  }

  double _calculateConfidence(CategorySpendingPattern pattern, BudgetApproach approach) {
    double baseConfidence = pattern.consistency;
    
    // Adjust based on approach
    switch (approach) {
      case BudgetApproach.conservative:
        baseConfidence += 0.2; // Conservative is safer
        break;
      case BudgetApproach.balanced:
        baseConfidence += 0.1; // Balanced is moderately safe
        break;
      case BudgetApproach.aggressive:
        baseConfidence -= 0.1; // Aggressive is riskier
        break;
    }
    
    return baseConfidence.clamp(0.0, 1.0);
  }

  String _getConservativeReasoning(CategorySpendingPattern pattern, double buffer) {
    return 'Based on your highest spending month (${pattern.maxAmount.toStringAsFixed(0)}) plus ${(buffer * 100).toInt()}% buffer for unexpected expenses.';
  }

  String _getBalancedReasoning(CategorySpendingPattern pattern) {
    return 'Based on your average spending (${pattern.averageAmount.toStringAsFixed(0)}) with adjustment for spending variability.';
  }

  String _getAggressiveReasoning(CategorySpendingPattern pattern, double reduction) {
    return 'Reduced by ${(reduction * 100).toInt()}% from average to encourage savings. Monitor closely to ensure feasibility.';
  }
}

// Data models for budget suggestions
class BudgetSuggestionPlan {
  final int analysisMonths;
  final double totalHistoricalIncome;
  final double totalHistoricalExpenses;
  final List<BudgetSuggestion> conservativePlan;
  final List<BudgetSuggestion> balancedPlan;
  final List<BudgetSuggestion> aggressivePlan;
  final Map<String, CategorySpendingPattern> categoryAnalysis;
  final List<BudgetInsight> insights;
  final List<Budget> currentBudgets;
  final DateTime generatedAt;

  BudgetSuggestionPlan({
    required this.analysisMonths,
    required this.totalHistoricalIncome,
    required this.totalHistoricalExpenses,
    required this.conservativePlan,
    required this.balancedPlan,
    required this.aggressivePlan,
    required this.categoryAnalysis,
    required this.insights,
    required this.currentBudgets,
    required this.generatedAt,
  });

  double get historicalSavingsRate => totalHistoricalIncome > 0 
      ? ((totalHistoricalIncome - totalHistoricalExpenses) / totalHistoricalIncome) * 100 
      : 0.0;

  double getTotalBudgetForPlan(BudgetApproach approach) {
    switch (approach) {
      case BudgetApproach.conservative:
        return conservativePlan.fold(0.0, (sum, suggestion) => sum + suggestion.suggestedAmount);
      case BudgetApproach.balanced:
        return balancedPlan.fold(0.0, (sum, suggestion) => sum + suggestion.suggestedAmount);
      case BudgetApproach.aggressive:
        return aggressivePlan.fold(0.0, (sum, suggestion) => sum + suggestion.suggestedAmount);
    }
  }

  double getProjectedSavingsForPlan(BudgetApproach approach) {
    return totalHistoricalIncome - getTotalBudgetForPlan(approach);
  }

  double getProjectedSavingsRateForPlan(BudgetApproach approach) {
    return totalHistoricalIncome > 0 
        ? (getProjectedSavingsForPlan(approach) / totalHistoricalIncome) * 100 
        : 0.0;
  }
}

class BudgetSuggestion {
  final String categoryName;
  final double suggestedAmount;
  final String reasoning;
  final double confidence;
  final double priority;

  BudgetSuggestion({
    required this.categoryName,
    required this.suggestedAmount,
    required this.reasoning,
    required this.confidence,
    required this.priority,
  });
}

class CategorySpendingPattern {
  final String categoryName;
  final double averageAmount;
  final double maxAmount;
  final double minAmount;
  final double volatility;
  final SpendingTrend trend;
  final double consistency;
  final double priority;

  CategorySpendingPattern({
    required this.categoryName,
    required this.averageAmount,
    required this.maxAmount,
    required this.minAmount,
    required this.volatility,
    required this.trend,
    required this.consistency,
    required this.priority,
  });
}

class SpendingAnalysis {
  final double averageIncome;
  final double averageExpenses;
  final Map<String, CategorySpendingPattern> categoryAnalysis;
  final int analysisMonths;

  SpendingAnalysis({
    required this.averageIncome,
    required this.averageExpenses,
    required this.categoryAnalysis,
    required this.analysisMonths,
  });
}

class BudgetInsight {
  final InsightType type;
  final String title;
  final String description;
  final bool actionable;
  final InsightPriority priority;

  BudgetInsight({
    required this.type,
    required this.title,
    required this.description,
    required this.actionable,
    required this.priority,
  });
}

enum BudgetApproach { conservative, balanced, aggressive }
enum SpendingTrend { increasing, decreasing, stable }
enum InsightType { positive, warning, info }
enum InsightPriority { high, medium, low }

// Extension methods
extension BudgetApproachExtension on BudgetApproach {
  String get displayName {
    switch (this) {
      case BudgetApproach.conservative:
        return 'Conservative';
      case BudgetApproach.balanced:
        return 'Balanced';
      case BudgetApproach.aggressive:
        return 'Aggressive';
    }
  }

  String get description {
    switch (this) {
      case BudgetApproach.conservative:
        return 'Higher budgets with safety buffer for unexpected expenses';
      case BudgetApproach.balanced:
        return 'Moderate budgets based on average spending patterns';
      case BudgetApproach.aggressive:
        return 'Lower budgets to maximize savings potential';
    }
  }

  String get icon {
    switch (this) {
      case BudgetApproach.conservative:
        return '🛡️';
      case BudgetApproach.balanced:
        return '⚖️';
      case BudgetApproach.aggressive:
        return '🎯';
    }
  }
}
