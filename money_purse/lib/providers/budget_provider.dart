import 'package:flutter/foundation.dart';
import '../models/budget.dart';
import '../services/budget_service.dart';

class BudgetProvider extends ChangeNotifier {
  final BudgetService _budgetService = BudgetService();
  
  List<Budget> _budgets = [];
  List<Budget> _currentBudgets = [];
  BudgetStatistics? _statistics;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Budget> get budgets => _budgets;
  List<Budget> get currentBudgets => _currentBudgets;
  BudgetStatistics? get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Filtered budgets by status
  List<Budget> get onTrackBudgets => _currentBudgets
      .where((budget) => budget.status == BudgetStatus.onTrack)
      .toList();

  List<Budget> get nearLimitBudgets => _currentBudgets
      .where((budget) => budget.status == BudgetStatus.nearLimit)
      .toList();

  List<Budget> get exceededBudgets => _currentBudgets
      .where((budget) => budget.status == BudgetStatus.exceeded)
      .toList();

  /// Initialize the provider
  Future<void> initialize() async {
    await _budgetService.initializeBudgetTable();
    await loadBudgets();
    await updateBudgetSpentAmounts();
  }

  /// Load all budgets
  Future<void> loadBudgets() async {
    _setLoading(true);
    try {
      _budgets = await _budgetService.getAllBudgets();
      _currentBudgets = await _budgetService.getCurrentBudgets();
      _statistics = await _budgetService.getBudgetStatistics();
      _clearError();
    } catch (e) {
      _setError('Failed to load budgets: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new budget
  Future<void> createBudget({
    required String categoryName,
    required double budgetAmount,
    required BudgetPeriod period,
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) async {
    try {
      _setLoading(true);

      // Calculate start and end dates
      final startDate = customStartDate ?? DateTime.now();
      final endDate = customEndDate ?? _calculateEndDate(startDate, period);

      // Check if budget already exists for this category and period
      final existingBudget = await _budgetService.getBudgetForCategory(categoryName);
      if (existingBudget != null) {
        throw Exception('Budget already exists for category "$categoryName"');
      }

      final budget = Budget(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        categoryName: categoryName,
        budgetAmount: budgetAmount,
        period: period,
        startDate: startDate,
        endDate: endDate,
        createdAt: DateTime.now(),
      );

      await _budgetService.createBudget(budget);
      await loadBudgets(); // Refresh data
      _clearError();
    } catch (e) {
      _setError('Failed to create budget: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing budget
  Future<void> updateBudget(Budget budget) async {
    try {
      _setLoading(true);
      await _budgetService.updateBudget(budget);
      await loadBudgets(); // Refresh data
      _clearError();
    } catch (e) {
      _setError('Failed to update budget: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a budget
  Future<void> deleteBudget(String budgetId) async {
    try {
      _setLoading(true);
      await _budgetService.deleteBudget(budgetId);
      await loadBudgets(); // Refresh data
      _clearError();
    } catch (e) {
      _setError('Failed to delete budget: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Deactivate a budget
  Future<void> deactivateBudget(String budgetId) async {
    try {
      await _budgetService.deactivateBudget(budgetId);
      await loadBudgets(); // Refresh data
      _clearError();
    } catch (e) {
      _setError('Failed to deactivate budget: $e');
    }
  }

  /// Update spent amounts for all budgets
  Future<void> updateBudgetSpentAmounts() async {
    try {
      await _budgetService.updateAllBudgetSpentAmounts();
      await loadBudgets(); // Refresh data
    } catch (e) {
      _setError('Failed to update budget spent amounts: $e');
    }
  }

  /// Get budget for specific category
  Future<Budget?> getBudgetForCategory(String categoryName) async {
    try {
      return await _budgetService.getBudgetForCategory(categoryName);
    } catch (e) {
      _setError('Failed to get budget for category: $e');
      return null;
    }
  }

  /// Get budgets expiring soon
  Future<List<Budget>> getBudgetsExpiringSoon() async {
    try {
      return await _budgetService.getBudgetsExpiringSoon();
    } catch (e) {
      _setError('Failed to get expiring budgets: $e');
      return [];
    }
  }

  /// Create budget for next period
  Future<void> renewBudget(Budget currentBudget) async {
    try {
      _setLoading(true);
      await _budgetService.createNextPeriodBudget(currentBudget);
      await loadBudgets(); // Refresh data
      _clearError();
    } catch (e) {
      _setError('Failed to renew budget: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Check if category has active budget
  bool hasBudgetForCategory(String categoryName) {
    return _currentBudgets.any((budget) => budget.categoryName == categoryName);
  }

  /// Get budget progress for category
  double getBudgetProgressForCategory(String categoryName) {
    final budget = _currentBudgets
        .where((b) => b.categoryName == categoryName)
        .firstOrNull;
    return budget?.usagePercentage ?? 0.0;
  }

  /// Get remaining budget for category
  double getRemainingBudgetForCategory(String categoryName) {
    final budget = _currentBudgets
        .where((b) => b.categoryName == categoryName)
        .firstOrNull;
    return budget?.remainingAmount ?? 0.0;
  }

  /// Check if spending would exceed budget
  bool wouldExceedBudget(String categoryName, double amount) {
    final budget = _currentBudgets
        .where((b) => b.categoryName == categoryName)
        .firstOrNull;
    if (budget == null) return false;
    return (budget.spentAmount + amount) > budget.budgetAmount;
  }

  /// Get budget alerts
  List<BudgetAlert> getBudgetAlerts() {
    final alerts = <BudgetAlert>[];
    
    for (final budget in _currentBudgets) {
      if (budget.status == BudgetStatus.exceeded) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.exceeded,
          budget: budget,
          message: 'Budget exceeded for ${budget.categoryName}',
        ));
      } else if (budget.status == BudgetStatus.nearLimit) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.nearLimit,
          budget: budget,
          message: '${budget.categoryName} budget is ${(budget.usagePercentage * 100).toInt()}% used',
        ));
      }
      
      if (budget.daysRemaining <= 3 && budget.daysRemaining > 0) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.expiringSoon,
          budget: budget,
          message: '${budget.categoryName} budget expires in ${budget.daysRemaining} days',
        ));
      }
    }
    
    return alerts;
  }

  /// Calculate end date based on period
  DateTime _calculateEndDate(DateTime startDate, BudgetPeriod period) {
    switch (period) {
      case BudgetPeriod.weekly:
        return startDate.add(const Duration(days: 7));
      case BudgetPeriod.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case BudgetPeriod.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case BudgetPeriod.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
      case BudgetPeriod.custom:
        return startDate.add(const Duration(days: 30));
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Refresh all data
  Future<void> refresh() async {
    await loadBudgets();
    await updateBudgetSpentAmounts();
  }
}

/// Budget alert model
class BudgetAlert {
  final BudgetAlertType type;
  final Budget budget;
  final String message;

  BudgetAlert({
    required this.type,
    required this.budget,
    required this.message,
  });
}

enum BudgetAlertType {
  exceeded,
  nearLimit,
  expiringSoon,
}
