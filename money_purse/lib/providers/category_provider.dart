import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/category.dart' as app_models;
import '../models/transaction.dart';
import '../services/database_service.dart';

/// Category usage statistics model
class CategoryStats {
  final String categoryName;
  final int usageCount;
  final double totalAmount;
  final DateTime lastUsed;
  final TransactionType transactionType;

  CategoryStats({
    required this.categoryName,
    required this.usageCount,
    required this.totalAmount,
    required this.lastUsed,
    required this.transactionType,
  });

  CategoryStats copyWith({
    String? categoryName,
    int? usageCount,
    double? totalAmount,
    DateTime? lastUsed,
    TransactionType? transactionType,
  }) {
    return CategoryStats(
      categoryName: categoryName ?? this.categoryName,
      usageCount: usageCount ?? this.usageCount,
      totalAmount: totalAmount ?? this.totalAmount,
      lastUsed: lastUsed ?? this.lastUsed,
      transactionType: transactionType ?? this.transactionType,
    );
  }

  double get averageAmount => usageCount > 0 ? totalAmount / usageCount : 0.0;
}

class CategoryProvider extends ChangeNotifier {
  List<app_models.Category> _customCategories = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<app_models.Category> get customCategories => _customCategories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the provider and load custom categories
  Future<void> initialize() async {
    await loadCustomCategories();
  }

  /// Get all categories (predefined + custom) by type
  List<app_models.Category> getCategoriesByType(TransactionType type) {
    final predefinedCategories = app_models.Categories.getCategoriesByType(
      type,
    );
    final customCategoriesOfType = _customCategories
        .where((category) => category.type == type)
        .toList();

    return [...predefinedCategories, ...customCategoriesOfType];
  }

  /// Get all categories (predefined + custom)
  List<app_models.Category> getAllCategories() {
    return [...app_models.Categories.getAllCategories(), ..._customCategories];
  }

  /// Find category by name (searches both predefined and custom)
  app_models.Category? findCategoryByName(String name) {
    // First check predefined categories
    final predefinedCategory = app_models.Categories.findCategoryByName(name);
    if (predefinedCategory != null) {
      return predefinedCategory;
    }

    // Then check custom categories
    try {
      return _customCategories.firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Add a new custom category
  Future<void> addCategory(app_models.Category category) async {
    try {
      _setLoading(true);

      // Check if category name already exists
      if (findCategoryByName(category.name) != null) {
        throw Exception('Category "${category.name}" already exists');
      }

      _customCategories.add(category);
      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing custom category
  Future<void> updateCategory(
    String oldName,
    app_models.Category updatedCategory,
  ) async {
    try {
      _setLoading(true);

      final index = _customCategories.indexWhere((cat) => cat.name == oldName);
      if (index == -1) {
        throw Exception('Category "$oldName" not found');
      }

      // Check if new name conflicts with existing categories (except the one being updated)
      if (oldName != updatedCategory.name &&
          findCategoryByName(updatedCategory.name) != null) {
        throw Exception('Category "${updatedCategory.name}" already exists');
      }

      _customCategories[index] = updatedCategory;
      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a custom category
  Future<void> deleteCategory(String categoryName) async {
    try {
      _setLoading(true);

      // Check if it's a predefined category
      if (app_models.Categories.findCategoryByName(categoryName) != null) {
        throw Exception('Cannot delete predefined category');
      }

      _customCategories.removeWhere(
        (category) => category.name == categoryName,
      );
      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a subcategory to an existing category
  Future<void> addSubcategory(String categoryName, String subcategory) async {
    try {
      _setLoading(true);

      final categoryIndex = _customCategories.indexWhere(
        (cat) => cat.name == categoryName,
      );
      if (categoryIndex == -1) {
        throw Exception('Category "$categoryName" not found');
      }

      final category = _customCategories[categoryIndex];
      if (category.subcategories.contains(subcategory)) {
        throw Exception('Subcategory "$subcategory" already exists');
      }

      final updatedSubcategories = [...category.subcategories, subcategory];
      final updatedCategory = app_models.Category(
        name: category.name,
        icon: category.icon,
        type: category.type,
        subcategories: updatedSubcategories,
      );

      _customCategories[categoryIndex] = updatedCategory;
      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add subcategory: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Remove a subcategory from an existing category
  Future<void> removeSubcategory(
    String categoryName,
    String subcategory,
  ) async {
    try {
      _setLoading(true);

      final categoryIndex = _customCategories.indexWhere(
        (cat) => cat.name == categoryName,
      );
      if (categoryIndex == -1) {
        throw Exception('Category "$categoryName" not found');
      }

      final category = _customCategories[categoryIndex];
      final updatedSubcategories = category.subcategories
          .where((sub) => sub != subcategory)
          .toList();

      final updatedCategory = app_models.Category(
        name: category.name,
        icon: category.icon,
        type: category.type,
        subcategories: updatedSubcategories,
      );

      _customCategories[categoryIndex] = updatedCategory;
      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove subcategory: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load custom categories from SharedPreferences
  Future<void> loadCustomCategories() async {
    try {
      _setLoading(true);
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('custom_categories');

      if (categoriesJson != null) {
        final List<dynamic> categoriesList = json.decode(categoriesJson);
        _customCategories = categoriesList
            .map((categoryMap) => _categoryFromMap(categoryMap))
            .toList();
      }

      _clearError();
    } catch (e) {
      _setError('Failed to load categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Save custom categories to SharedPreferences
  Future<void> _saveCustomCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = json.encode(
      _customCategories.map((category) => _categoryToMap(category)).toList(),
    );
    await prefs.setString('custom_categories', categoriesJson);
  }

  /// Convert Category to Map for JSON serialization
  Map<String, dynamic> _categoryToMap(app_models.Category category) {
    return {
      'name': category.name,
      'icon': category.icon,
      'type': category.type.toString(),
      'subcategories': category.subcategories,
    };
  }

  /// Convert Map to Category for JSON deserialization
  app_models.Category _categoryFromMap(Map<String, dynamic> map) {
    return app_models.Category(
      name: map['name'],
      icon: map['icon'],
      type: TransactionType.values.firstWhere(
        (type) => type.toString() == map['type'],
      ),
      subcategories: List<String>.from(map['subcategories']),
    );
  }

  /// Get category usage statistics
  Future<Map<String, CategoryStats>> getCategoryUsageStats() async {
    final Map<String, CategoryStats> stats = {};
    final databaseService = DatabaseService();

    try {
      // Get all transactions
      final transactions = await databaseService.getAllTransactions();

      // Count usage for each category
      for (final transaction in transactions) {
        final categoryName = transaction.category;
        if (stats.containsKey(categoryName)) {
          stats[categoryName] = stats[categoryName]!.copyWith(
            usageCount: stats[categoryName]!.usageCount + 1,
            totalAmount: stats[categoryName]!.totalAmount + transaction.amount,
            lastUsed: transaction.date.isAfter(stats[categoryName]!.lastUsed)
                ? transaction.date
                : stats[categoryName]!.lastUsed,
          );
        } else {
          stats[categoryName] = CategoryStats(
            categoryName: categoryName,
            usageCount: 1,
            totalAmount: transaction.amount,
            lastUsed: transaction.date,
            transactionType: transaction.type,
          );
        }
      }

      return stats;
    } catch (e) {
      return {};
    }
  }

  /// Reset all custom categories
  Future<void> resetCustomCategories() async {
    try {
      _setLoading(true);
      _customCategories.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('custom_categories');
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to reset categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Export custom categories as JSON
  String exportCategories() {
    return json.encode(
      _customCategories.map((category) => _categoryToMap(category)).toList(),
    );
  }

  /// Import custom categories from JSON
  Future<void> importCategories(String categoriesJson) async {
    try {
      _setLoading(true);
      final List<dynamic> categoriesList = json.decode(categoriesJson);
      final importedCategories = categoriesList
          .map((categoryMap) => _categoryFromMap(categoryMap))
          .toList();

      // Add imported categories (skip duplicates)
      for (final category in importedCategories) {
        if (findCategoryByName(category.name) == null) {
          _customCategories.add(category);
        }
      }

      await _saveCustomCategories();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to import categories: $e');
    } finally {
      _setLoading(false);
    }
  }
}
