import 'package:flutter/foundation.dart';
import '../models/transaction.dart';
import '../services/database_service.dart';
import '../services/sms_service.dart';
import '../services/transaction_parser.dart';
import '../services/permission_service.dart';

class TransactionProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final SmsService _smsService = SmsService();
  final TransactionParser _transactionParser = TransactionParser();
  final PermissionService _permissionService = PermissionService();

  List<Transaction> _transactions = [];
  List<ParsedTransaction> _pendingTransactions = [];
  bool _isLoading = false;
  String? _error;
  bool _hasPermission = false;

  // Getters
  List<Transaction> get transactions => _transactions;
  List<ParsedTransaction> get pendingTransactions => _pendingTransactions;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasPermission => _hasPermission;

  // Filtered transactions
  List<Transaction> get expenses =>
      _transactions.where((t) => t.type == TransactionType.expense).toList();

  List<Transaction> get income =>
      _transactions.where((t) => t.type == TransactionType.income).toList();

  // Summary calculations
  double get totalExpenses => expenses.fold(0.0, (sum, t) => sum + t.amount);
  double get totalIncome => income.fold(0.0, (sum, t) => sum + t.amount);
  double get balance => totalIncome - totalExpenses;

  /// Initialize the provider
  Future<void> initialize() async {
    await _checkPermissions();
    await loadTransactions();
    if (_hasPermission) {
      await scanForNewTransactions();
    }
  }

  /// Check SMS permissions
  Future<void> _checkPermissions() async {
    try {
      _hasPermission = await _permissionService.isSmsPermissionGranted();
      notifyListeners();
    } catch (e) {
      _setError('Failed to check permissions: $e');
    }
  }

  /// Request SMS permission
  Future<bool> requestPermission() async {
    try {
      final result = await _permissionService.handleSmsPermissionFlow();
      _hasPermission = result.isGranted;
      notifyListeners();

      if (_hasPermission) {
        await scanForNewTransactions();
      }

      return _hasPermission;
    } catch (e) {
      _setError('Failed to request permission: $e');
      return false;
    }
  }

  /// Load transactions from database
  Future<void> loadTransactions() async {
    _setLoading(true);
    try {
      final transactions = await _databaseService.getAllTransactions();
      _transactions = transactions.cast<Transaction>();
      _clearError();
    } catch (e) {
      _setError('Failed to load transactions: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Scan for new transactions from SMS
  Future<void> scanForNewTransactions() async {
    if (!_hasPermission) {
      _setError('SMS permission not granted');
      return;
    }

    _setLoading(true);
    try {
      // Get processed SMS IDs to avoid duplicates
      final processedIds = await _databaseService.getProcessedSmsIds();

      // Get unprocessed transaction SMS
      final smsMessages = await _smsService.getUnprocessedTransactionSms(
        processedMessageIds: processedIds,
        days: 30,
      );

      // Parse SMS messages to transactions
      final parsedTransactions = _transactionParser.parseMultipleTransactions(
        smsMessages,
      );

      // Filter out low confidence transactions
      _pendingTransactions = parsedTransactions
          .where((t) => t.confidence > 0.5)
          .toList();

      _clearError();
    } catch (e) {
      _setError('Failed to scan for transactions: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new transaction
  Future<void> addTransaction(Transaction transaction) async {
    try {
      await _databaseService.insertTransaction(transaction);
      _transactions.insert(0, transaction);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add transaction: $e');
    }
  }

  /// Update an existing transaction
  Future<void> updateTransaction(Transaction transaction) async {
    try {
      await _databaseService.updateTransaction(transaction);
      final index = _transactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _transactions[index] = transaction;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update transaction: $e');
    }
  }

  /// Delete a transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      await _databaseService.deleteTransaction(transactionId);
      _transactions.removeWhere((t) => t.id == transactionId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete transaction: $e');
    }
  }

  /// Accept a pending transaction and save it
  Future<void> acceptPendingTransaction({
    required ParsedTransaction parsedTransaction,
    required String category,
    String? subcategory,
  }) async {
    try {
      final transaction = parsedTransaction.toTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        category: category,
        subcategory: subcategory,
      );

      await addTransaction(transaction);

      // Mark SMS as processed
      if (parsedTransaction.smsBody != null &&
          parsedTransaction.smsAddress != null) {
        await _databaseService.markSmsAsProcessed(
          smsId: _generateSmsId(parsedTransaction),
          smsAddress: parsedTransaction.smsAddress!,
          smsBody: parsedTransaction.smsBody!,
          smsDate: parsedTransaction.date,
          transactionId: transaction.id,
        );
      }

      // Remove from pending
      _pendingTransactions.removeWhere((t) => t == parsedTransaction);
      notifyListeners();
    } catch (e) {
      _setError('Failed to accept transaction: $e');
    }
  }

  /// Reject a pending transaction
  Future<void> rejectPendingTransaction(
    ParsedTransaction parsedTransaction,
  ) async {
    try {
      // Mark SMS as processed but don't create transaction
      if (parsedTransaction.smsBody != null &&
          parsedTransaction.smsAddress != null) {
        await _databaseService.markSmsAsProcessed(
          smsId: _generateSmsId(parsedTransaction),
          smsAddress: parsedTransaction.smsAddress!,
          smsBody: parsedTransaction.smsBody!,
          smsDate: parsedTransaction.date,
        );
      }

      // Remove from pending
      _pendingTransactions.removeWhere((t) => t == parsedTransaction);
      notifyListeners();
    } catch (e) {
      _setError('Failed to reject transaction: $e');
    }
  }

  /// Get transactions for a specific date range
  Future<List<Transaction>> getTransactionsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final transactions = await _databaseService.getTransactionsByDateRange(
        startDate: startDate,
        endDate: endDate,
      );
      return transactions.cast<Transaction>();
    } catch (e) {
      _setError('Failed to get transactions for date range: $e');
      return [];
    }
  }

  /// Get spending by category
  Future<Map<String, double>> getSpendingByCategory({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      return await _databaseService.getSpendingByCategory(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      _setError('Failed to get spending by category: $e');
      return {};
    }
  }

  /// Generate SMS ID for tracking
  String _generateSmsId(ParsedTransaction transaction) {
    final content =
        '${transaction.smsAddress}_${transaction.smsBody}_${transaction.date.millisecondsSinceEpoch}';
    return content.hashCode.toString();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Refresh all data
  Future<void> refresh() async {
    await loadTransactions();
    if (_hasPermission) {
      await scanForNewTransactions();
    }
  }
}
