class Transaction {
  final String id;
  final double amount;
  final String description;
  final String? merchant;
  final DateTime date;
  final TransactionType type;
  final String category;
  final String? subcategory;
  final String? smsBody;
  final String? smsAddress;
  final bool isVerified;
  final DateTime createdAt;

  Transaction({
    required this.id,
    required this.amount,
    required this.description,
    this.merchant,
    required this.date,
    required this.type,
    required this.category,
    this.subcategory,
    this.smsBody,
    this.smsAddress,
    this.isVerified = false,
    required this.createdAt,
  });

  // Convert Transaction to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'amount': amount,
      'description': description,
      'merchant': merchant,
      'date': date.millisecondsSinceEpoch,
      'type': type.toString(),
      'category': category,
      'subcategory': subcategory,
      'smsBody': smsBody,
      'smsAddress': smsAddress,
      'isVerified': isVerified ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  // Create Transaction from Map (database)
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      amount: map['amount'].toDouble(),
      description: map['description'],
      merchant: map['merchant'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      type: TransactionType.values.firstWhere(
        (e) => e.toString() == map['type'],
      ),
      category: map['category'],
      subcategory: map['subcategory'],
      smsBody: map['smsBody'],
      smsAddress: map['smsAddress'],
      isVerified: map['isVerified'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
    );
  }

  // Create a copy with updated fields
  Transaction copyWith({
    String? id,
    double? amount,
    String? description,
    String? merchant,
    DateTime? date,
    TransactionType? type,
    String? category,
    String? subcategory,
    String? smsBody,
    String? smsAddress,
    bool? isVerified,
    DateTime? createdAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      merchant: merchant ?? this.merchant,
      date: date ?? this.date,
      type: type ?? this.type,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      smsBody: smsBody ?? this.smsBody,
      smsAddress: smsAddress ?? this.smsAddress,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Transaction(id: $id, amount: $amount, description: $description, type: $type, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum TransactionType {
  expense,
  income,
  ignored,
}

// Extension to get display names for transaction types
extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.income:
        return 'Income';
      case TransactionType.ignored:
        return 'Ignored';
    }
  }

  String get icon {
    switch (this) {
      case TransactionType.expense:
        return '💸';
      case TransactionType.income:
        return '💰';
      case TransactionType.ignored:
        return '🚫';
    }
  }
}
