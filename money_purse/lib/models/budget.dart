class Budget {
  final String id;
  final String categoryName;
  final double budgetAmount;
  final double spentAmount;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastUpdated;

  Budget({
    required this.id,
    required this.categoryName,
    required this.budgetAmount,
    this.spentAmount = 0.0,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    required this.createdAt,
    this.lastUpdated,
  });

  // Convert Budget to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'categoryName': categoryName,
      'budgetAmount': budgetAmount,
      'spentAmount': spentAmount,
      'period': period.toString(),
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': lastUpdated?.millisecondsSinceEpoch,
    };
  }

  // Create Budget from Map (database)
  factory Budget.fromMap(Map<String, dynamic> map) {
    return Budget(
      id: map['id'],
      categoryName: map['categoryName'],
      budgetAmount: map['budgetAmount'].toDouble(),
      spentAmount: map['spentAmount'].toDouble(),
      period: BudgetPeriod.values.firstWhere(
        (e) => e.toString() == map['period'],
      ),
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      isActive: map['isActive'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      lastUpdated: map['lastUpdated'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastUpdated'])
          : null,
    );
  }

  // Create a copy with updated fields
  Budget copyWith({
    String? id,
    String? categoryName,
    double? budgetAmount,
    double? spentAmount,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return Budget(
      id: id ?? this.id,
      categoryName: categoryName ?? this.categoryName,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      spentAmount: spentAmount ?? this.spentAmount,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Calculate remaining budget
  double get remainingAmount => budgetAmount - spentAmount;

  // Calculate budget usage percentage (0.0 to 1.0)
  double get usagePercentage =>
      budgetAmount > 0 ? (spentAmount / budgetAmount).clamp(0.0, 1.0) : 0.0;

  // Check if budget is exceeded
  bool get isExceeded => spentAmount > budgetAmount;

  // Check if budget is close to limit (80% or more)
  bool get isNearLimit => usagePercentage >= 0.8;

  // Check if budget period is current
  bool get isCurrent {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate) && isActive;
  }

  // Get budget status
  BudgetStatus get status {
    if (!isActive) return BudgetStatus.inactive;
    if (!isCurrent) return BudgetStatus.expired;
    if (isExceeded) return BudgetStatus.exceeded;
    if (isNearLimit) return BudgetStatus.nearLimit;
    return BudgetStatus.onTrack;
  }

  // Get days remaining in budget period
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }

  // Get days elapsed in budget period
  int get daysElapsed {
    final now = DateTime.now();
    if (now.isBefore(startDate)) return 0;
    final elapsed = now.difference(startDate).inDays;
    final total = endDate.difference(startDate).inDays;
    return elapsed > total ? total : elapsed;
  }

  // Get total days in budget period
  int get totalDays => endDate.difference(startDate).inDays;

  // Calculate daily spending rate
  double get dailySpendingRate {
    if (daysElapsed <= 0) return 0.0;
    return spentAmount / daysElapsed;
  }

  // Calculate projected spending for the period
  double get projectedSpending {
    if (dailySpendingRate <= 0) return spentAmount;
    return dailySpendingRate * totalDays;
  }

  @override
  String toString() {
    return 'Budget(id: $id, category: $categoryName, amount: $budgetAmount, spent: $spentAmount, period: $period)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Budget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum BudgetPeriod { weekly, monthly, quarterly, yearly, custom }

enum BudgetStatus { onTrack, nearLimit, exceeded, expired, inactive }

// Extension to get display names and colors for budget periods
extension BudgetPeriodExtension on BudgetPeriod {
  String get displayName {
    switch (this) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.yearly:
        return 'Yearly';
      case BudgetPeriod.custom:
        return 'Custom';
    }
  }

  int get durationInDays {
    switch (this) {
      case BudgetPeriod.weekly:
        return 7;
      case BudgetPeriod.monthly:
        return 30;
      case BudgetPeriod.quarterly:
        return 90;
      case BudgetPeriod.yearly:
        return 365;
      case BudgetPeriod.custom:
        return 30; // Default for custom
    }
  }
}

// Extension to get display names and colors for budget status
extension BudgetStatusExtension on BudgetStatus {
  String get displayName {
    switch (this) {
      case BudgetStatus.onTrack:
        return 'On Track';
      case BudgetStatus.nearLimit:
        return 'Near Limit';
      case BudgetStatus.exceeded:
        return 'Exceeded';
      case BudgetStatus.expired:
        return 'Expired';
      case BudgetStatus.inactive:
        return 'Inactive';
    }
  }

  String get icon {
    switch (this) {
      case BudgetStatus.onTrack:
        return '✅';
      case BudgetStatus.nearLimit:
        return '⚠️';
      case BudgetStatus.exceeded:
        return '🚨';
      case BudgetStatus.expired:
        return '⏰';
      case BudgetStatus.inactive:
        return '⏸️';
    }
  }
}
