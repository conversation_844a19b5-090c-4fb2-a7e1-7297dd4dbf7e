class MonthlyReport {
  final int year;
  final int month;
  final double totalIncome;
  final double totalExpenses;
  final double netSavings;
  final Map<String, double> expensesByCategory;
  final Map<String, double> incomeByCategory;
  final List<DailyTransaction> dailyTransactions;
  final int totalTransactionCount;
  final double averageDailyExpense;
  final double averageDailyIncome;
  final String topExpenseCategory;
  final String topIncomeCategory;
  final double budgetUtilization;
  final List<BudgetComparison> budgetComparisons;

  MonthlyReport({
    required this.year,
    required this.month,
    required this.totalIncome,
    required this.totalExpenses,
    required this.expensesByCategory,
    required this.incomeByCategory,
    required this.dailyTransactions,
    required this.totalTransactionCount,
    required this.budgetComparisons,
  })  : netSavings = totalIncome - totalExpenses,
        averageDailyExpense = totalExpenses / DateTime(year, month + 1, 0).day,
        averageDailyIncome = totalIncome / DateTime(year, month + 1, 0).day,
        topExpenseCategory = expensesByCategory.isNotEmpty
            ? expensesByCategory.entries
                .reduce((a, b) => a.value > b.value ? a : b)
                .key
            : 'None',
        topIncomeCategory = incomeByCategory.isNotEmpty
            ? incomeByCategory.entries
                .reduce((a, b) => a.value > b.value ? a : b)
                .key
            : 'None',
        budgetUtilization = budgetComparisons.isNotEmpty
            ? budgetComparisons
                    .map((b) => b.utilizationPercentage)
                    .reduce((a, b) => a + b) /
                budgetComparisons.length
            : 0.0;

  String get monthName {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  String get displayTitle => '$monthName $year';

  bool get isPositiveSavings => netSavings > 0;

  double get savingsRate => totalIncome > 0 ? (netSavings / totalIncome) * 100 : 0.0;

  int get daysInMonth => DateTime(year, month + 1, 0).day;

  // Get expense trend (compared to previous month)
  ExpenseTrend getExpenseTrend(MonthlyReport? previousMonth) {
    if (previousMonth == null) return ExpenseTrend.neutral;
    
    final difference = totalExpenses - previousMonth.totalExpenses;
    final percentageChange = previousMonth.totalExpenses > 0 
        ? (difference / previousMonth.totalExpenses) * 100 
        : 0.0;

    if (percentageChange > 5) return ExpenseTrend.increasing;
    if (percentageChange < -5) return ExpenseTrend.decreasing;
    return ExpenseTrend.stable;
  }

  // Get income trend (compared to previous month)
  IncomeTrend getIncomeTrend(MonthlyReport? previousMonth) {
    if (previousMonth == null) return IncomeTrend.neutral;
    
    final difference = totalIncome - previousMonth.totalIncome;
    final percentageChange = previousMonth.totalIncome > 0 
        ? (difference / previousMonth.totalIncome) * 100 
        : 0.0;

    if (percentageChange > 5) return IncomeTrend.increasing;
    if (percentageChange < -5) return IncomeTrend.decreasing;
    return IncomeTrend.stable;
  }

  // Get top spending days
  List<DailyTransaction> getTopSpendingDays({int limit = 5}) {
    final sortedDays = List<DailyTransaction>.from(dailyTransactions);
    sortedDays.sort((a, b) => b.totalExpenses.compareTo(a.totalExpenses));
    return sortedDays.take(limit).toList();
  }

  // Get category growth compared to previous month
  Map<String, double> getCategoryGrowth(MonthlyReport? previousMonth) {
    if (previousMonth == null) return {};
    
    final growth = <String, double>{};
    
    for (final category in expensesByCategory.keys) {
      final currentAmount = expensesByCategory[category] ?? 0.0;
      final previousAmount = previousMonth.expensesByCategory[category] ?? 0.0;
      
      if (previousAmount > 0) {
        growth[category] = ((currentAmount - previousAmount) / previousAmount) * 100;
      } else if (currentAmount > 0) {
        growth[category] = 100.0; // New category
      }
    }
    
    return growth;
  }
}

class DailyTransaction {
  final int day;
  final double totalIncome;
  final double totalExpenses;
  final int transactionCount;

  DailyTransaction({
    required this.day,
    required this.totalIncome,
    required this.totalExpenses,
    required this.transactionCount,
  });

  double get netAmount => totalIncome - totalExpenses;
}

class BudgetComparison {
  final String categoryName;
  final double budgetAmount;
  final double actualAmount;
  final double utilizationPercentage;
  final bool isOverBudget;

  BudgetComparison({
    required this.categoryName,
    required this.budgetAmount,
    required this.actualAmount,
  })  : utilizationPercentage = budgetAmount > 0 ? (actualAmount / budgetAmount) * 100 : 0.0,
        isOverBudget = actualAmount > budgetAmount;

  double get remainingAmount => budgetAmount - actualAmount;
  double get overageAmount => isOverBudget ? actualAmount - budgetAmount : 0.0;
}

enum ExpenseTrend {
  increasing,
  decreasing,
  stable,
  neutral,
}

enum IncomeTrend {
  increasing,
  decreasing,
  stable,
  neutral,
}

// Extension methods for trend enums
extension ExpenseTrendExtension on ExpenseTrend {
  String get displayName {
    switch (this) {
      case ExpenseTrend.increasing:
        return 'Increasing';
      case ExpenseTrend.decreasing:
        return 'Decreasing';
      case ExpenseTrend.stable:
        return 'Stable';
      case ExpenseTrend.neutral:
        return 'No Data';
    }
  }

  String get icon {
    switch (this) {
      case ExpenseTrend.increasing:
        return '📈';
      case ExpenseTrend.decreasing:
        return '📉';
      case ExpenseTrend.stable:
        return '➡️';
      case ExpenseTrend.neutral:
        return '➖';
    }
  }

  bool get isPositive => this == ExpenseTrend.decreasing;
}

extension IncomeTrendExtension on IncomeTrend {
  String get displayName {
    switch (this) {
      case IncomeTrend.increasing:
        return 'Increasing';
      case IncomeTrend.decreasing:
        return 'Decreasing';
      case IncomeTrend.stable:
        return 'Stable';
      case IncomeTrend.neutral:
        return 'No Data';
    }
  }

  String get icon {
    switch (this) {
      case IncomeTrend.increasing:
        return '📈';
      case IncomeTrend.decreasing:
        return '📉';
      case IncomeTrend.stable:
        return '➡️';
      case IncomeTrend.neutral:
        return '➖';
    }
  }

  bool get isPositive => this == IncomeTrend.increasing;
}

// Monthly comparison model
class MonthlyComparison {
  final MonthlyReport currentMonth;
  final MonthlyReport? previousMonth;
  final MonthlyReport? sameMonthLastYear;

  MonthlyComparison({
    required this.currentMonth,
    this.previousMonth,
    this.sameMonthLastYear,
  });

  // Month-over-month changes
  double get expenseChange => previousMonth != null 
      ? currentMonth.totalExpenses - previousMonth!.totalExpenses 
      : 0.0;

  double get incomeChange => previousMonth != null 
      ? currentMonth.totalIncome - previousMonth!.totalIncome 
      : 0.0;

  double get savingsChange => previousMonth != null 
      ? currentMonth.netSavings - previousMonth!.netSavings 
      : 0.0;

  // Year-over-year changes
  double get yearOverYearExpenseChange => sameMonthLastYear != null 
      ? currentMonth.totalExpenses - sameMonthLastYear!.totalExpenses 
      : 0.0;

  double get yearOverYearIncomeChange => sameMonthLastYear != null 
      ? currentMonth.totalIncome - sameMonthLastYear!.totalIncome 
      : 0.0;

  // Percentage changes
  double get expenseChangePercentage => previousMonth != null && previousMonth!.totalExpenses > 0
      ? (expenseChange / previousMonth!.totalExpenses) * 100
      : 0.0;

  double get incomeChangePercentage => previousMonth != null && previousMonth!.totalIncome > 0
      ? (incomeChange / previousMonth!.totalIncome) * 100
      : 0.0;
}
