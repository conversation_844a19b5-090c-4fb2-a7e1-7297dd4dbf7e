import 'transaction.dart';

class Category {
  final String name;
  final String icon;
  final TransactionType type;
  final List<String> subcategories;

  const Category({
    required this.name,
    required this.icon,
    required this.type,
    required this.subcategories,
  });
}

// Predefined categories for expenses and income
class Categories {
  static const List<Category> expenseCategories = [
    Category(
      name: 'Food & Dining',
      icon: '🍽️',
      type: TransactionType.expense,
      subcategories: [
        'Restaurants',
        'Groceries',
        'Fast Food',
        'Coffee & Tea',
        'Delivery',
      ],
    ),
    Category(
      name: 'Transportation',
      icon: '🚗',
      type: TransactionType.expense,
      subcategories: [
        'Fuel',
        'Public Transport',
        'Taxi/Ride Share',
        'Parking',
        'Vehicle Maintenance',
      ],
    ),
    Category(
      name: 'Shopping',
      icon: '🛍️',
      type: TransactionType.expense,
      subcategories: [
        'Clothing',
        'Electronics',
        'Home & Garden',
        'Books',
        'General Shopping',
      ],
    ),
    Category(
      name: 'Entertainment',
      icon: '🎬',
      type: TransactionType.expense,
      subcategories: [
        'Movies',
        'Games',
        'Sports',
        'Music',
        'Events',
      ],
    ),
    Category(
      name: 'Bills & Utilities',
      icon: '📄',
      type: TransactionType.expense,
      subcategories: [
        'Electricity',
        'Water',
        'Internet',
        'Phone',
        'Insurance',
      ],
    ),
    Category(
      name: 'Healthcare',
      icon: '🏥',
      type: TransactionType.expense,
      subcategories: [
        'Doctor',
        'Pharmacy',
        'Dental',
        'Hospital',
        'Health Insurance',
      ],
    ),
    Category(
      name: 'Education',
      icon: '📚',
      type: TransactionType.expense,
      subcategories: [
        'Tuition',
        'Books',
        'Courses',
        'Training',
        'School Supplies',
      ],
    ),
    Category(
      name: 'Personal Care',
      icon: '💄',
      type: TransactionType.expense,
      subcategories: [
        'Haircut',
        'Cosmetics',
        'Gym',
        'Spa',
        'Personal Items',
      ],
    ),
    Category(
      name: 'Travel',
      icon: '✈️',
      type: TransactionType.expense,
      subcategories: [
        'Flights',
        'Hotels',
        'Car Rental',
        'Vacation',
        'Business Travel',
      ],
    ),
    Category(
      name: 'Other',
      icon: '📦',
      type: TransactionType.expense,
      subcategories: [
        'Gifts',
        'Donations',
        'Fees',
        'Miscellaneous',
      ],
    ),
  ];

  static const List<Category> incomeCategories = [
    Category(
      name: 'Salary',
      icon: '💼',
      type: TransactionType.income,
      subcategories: [
        'Monthly Salary',
        'Bonus',
        'Overtime',
        'Commission',
      ],
    ),
    Category(
      name: 'Freelance',
      icon: '💻',
      type: TransactionType.income,
      subcategories: [
        'Consulting',
        'Design',
        'Writing',
        'Programming',
        'Other Services',
      ],
    ),
    Category(
      name: 'Business',
      icon: '🏢',
      type: TransactionType.income,
      subcategories: [
        'Sales',
        'Services',
        'Rental Income',
        'Profit',
      ],
    ),
    Category(
      name: 'Investments',
      icon: '📈',
      type: TransactionType.income,
      subcategories: [
        'Dividends',
        'Interest',
        'Capital Gains',
        'Crypto',
      ],
    ),
    Category(
      name: 'Other Income',
      icon: '💰',
      type: TransactionType.income,
      subcategories: [
        'Gifts',
        'Refunds',
        'Cashback',
        'Miscellaneous',
      ],
    ),
  ];

  static List<Category> getAllCategories() {
    return [...expenseCategories, ...incomeCategories];
  }

  static List<Category> getCategoriesByType(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return expenseCategories;
      case TransactionType.income:
        return incomeCategories;
      case TransactionType.ignored:
        return [];
    }
  }

  static Category? findCategoryByName(String name) {
    try {
      return getAllCategories().firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }
}
