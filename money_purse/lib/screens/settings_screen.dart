import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';
import '../providers/budget_provider.dart';
import '../services/sms_service.dart';
import 'category_management_screen.dart';
import 'budget_management_screen.dart';
import 'monthly_report_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SmsService _smsService = SmsService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // SMS Settings Section
          _buildSectionHeader('SMS Settings'),
          _buildSmsSettingsCard(),

          const SizedBox(height: 24),

          // Category Management Section
          _buildSectionHeader('Category Management'),
          _buildCategoryManagementCard(),

          const SizedBox(height: 24),

          // Data Management Section
          _buildSectionHeader('Data Management'),
          _buildDataManagementCard(),

          const SizedBox(height: 24),

          // App Information Section
          _buildSectionHeader('App Information'),
          _buildAppInfoCard(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSmsSettingsCard() {
    return Card(
      child: Column(
        children: [
          Consumer<TransactionProvider>(
            builder: (context, provider, child) {
              return ListTile(
                leading: Icon(
                  provider.hasPermission ? Icons.check_circle : Icons.error,
                  color: provider.hasPermission ? Colors.green : Colors.red,
                ),
                title: const Text('SMS Permission'),
                subtitle: Text(
                  provider.hasPermission
                      ? 'Granted - App can read SMS messages'
                      : 'Not granted - App cannot read SMS messages',
                ),
                trailing: provider.hasPermission
                    ? null
                    : ElevatedButton(
                        onPressed: () => provider.requestPermission(),
                        child: const Text('Grant'),
                      ),
              );
            },
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('Scan for New Transactions'),
            subtitle: const Text('Check SMS for new transaction messages'),
            trailing: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.arrow_forward_ios),
            onTap: _isLoading ? null : _scanForTransactions,
          ),

          const Divider(height: 1),

          FutureBuilder<SmsStats>(
            future: _smsService.getSmsStats(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final stats = snapshot.data!;
                return ListTile(
                  leading: const Icon(Icons.analytics),
                  title: const Text('SMS Statistics'),
                  subtitle: Text(
                    '${stats.totalMessages} transaction messages found in last ${stats.dateRange} days',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showSmsStats(stats),
                );
              }
              return const ListTile(
                leading: Icon(Icons.analytics),
                title: Text('SMS Statistics'),
                subtitle: Text('Loading...'),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryManagementCard() {
    return Card(
      child: Column(
        children: [
          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              return ListTile(
                leading: const Icon(Icons.category),
                title: const Text('Custom Categories'),
                subtitle: Text(
                  '${categoryProvider.customCategories.length} custom categories',
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _navigateToCategoryManagement(),
              );
            },
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.add_circle_outline),
            title: const Text('Add New Category'),
            subtitle: const Text('Create custom expense or income categories'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _navigateToCategoryManagement(),
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.download),
            title: const Text('Export Categories'),
            subtitle: const Text('Export custom categories as JSON'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _exportCategories(),
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.upload),
            title: const Text('Import Categories'),
            subtitle: const Text('Import categories from JSON file'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showImportDialog(),
          ),

          const Divider(height: 1),

          // Monthly Reports Section
          ListTile(
            leading: const Icon(Icons.assessment),
            title: const Text('Monthly Reports'),
            subtitle: const Text('View detailed monthly financial analysis'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _navigateToMonthlyReports(),
          ),

          const Divider(height: 1),

          // Budget Management Section
          ListTile(
            leading: const Icon(Icons.account_balance_wallet),
            title: const Text('Budget Management'),
            subtitle: Consumer<BudgetProvider>(
              builder: (context, budgetProvider, child) {
                final currentBudgets = budgetProvider.currentBudgets.length;
                return Text('$currentBudgets active budgets');
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _navigateToBudgetManagement(),
          ),

          const Divider(height: 1),

          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              return ListTile(
                leading: const Icon(Icons.restore),
                title: const Text('Reset Categories'),
                subtitle: const Text('Remove all custom categories'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showResetCategoriesDialog(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagementCard() {
    return Card(
      child: Column(
        children: [
          Consumer<TransactionProvider>(
            builder: (context, provider, child) {
              return ListTile(
                leading: const Icon(Icons.storage),
                title: const Text('Transaction Data'),
                subtitle: Text(
                  '${provider.transactions.length} transactions stored',
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showDataManagementOptions(),
              );
            },
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.download),
            title: const Text('Export Data'),
            subtitle: const Text('Export transactions to CSV file'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _exportData(),
          ),

          const Divider(height: 1),

          ListTile(
            leading: Icon(Icons.delete, color: Colors.red[600]),
            title: Text(
              'Clear All Data',
              style: TextStyle(color: Colors.red[600]),
            ),
            subtitle: const Text('Delete all transactions and settings'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showClearDataDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfoCard() {
    return Card(
      child: Column(
        children: [
          const ListTile(
            leading: Icon(Icons.info),
            title: Text('Version'),
            subtitle: Text('1.0.0'),
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('Privacy Policy'),
            subtitle: const Text('How we handle your data'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showPrivacyPolicy(),
          ),

          const Divider(height: 1),

          ListTile(
            leading: const Icon(Icons.help),
            title: const Text('Help & Support'),
            subtitle: const Text('Get help using the app'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showHelp(),
          ),
        ],
      ),
    );
  }

  Future<void> _scanForTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      await provider.scanForNewTransactions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Found ${provider.pendingTransactions.length} new transactions',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error scanning for transactions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSmsStats(SmsStats stats) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SMS Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Messages: ${stats.totalMessages}'),
            Text('Date Range: Last ${stats.dateRange} days'),
            const SizedBox(height: 16),
            const Text(
              'Top Senders:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            ...stats.topSenders
                .take(5)
                .map((entry) => Text('${entry.key}: ${entry.value} messages')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDataManagementOptions() {
    // Implementation for data management options
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data management options coming soon')),
    );
  }

  void _exportData() {
    // Implementation for data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data export feature coming soon')),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your transactions and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearAllData();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }

  void _clearAllData() {
    // Implementation for clearing all data
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Clear data feature coming soon')),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Money Purse processes your SMS messages locally on your device to detect financial transactions. '
            'No SMS data is transmitted to external servers or shared with third parties. '
            'All transaction data is stored locally on your device and can be deleted at any time.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'How to use Money Purse:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. Grant SMS permission to read transaction messages'),
              Text('2. Scan for new transactions from your SMS inbox'),
              Text('3. Review and categorize detected transactions'),
              Text(
                '4. View your expense and income summary on the home screen',
              ),
              SizedBox(height: 16),
              Text(
                'Supported Banks:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Most Indian banks (SBI, HDFC, ICICI, Axis, etc.)'),
              Text('• Payment services (Paytm, GPay, PhonePe, etc.)'),
              Text('• Credit card providers'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _navigateToCategoryManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CategoryManagementScreen()),
    );
  }

  void _navigateToBudgetManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BudgetManagementScreen()),
    );
  }

  void _navigateToMonthlyReports() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const MonthlyReportScreen()),
    );
  }

  void _showResetCategoriesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Categories'),
        content: const Text(
          'This will permanently delete all your custom categories. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<CategoryProvider>(
                context,
                listen: false,
              ).resetCustomCategories();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Custom categories reset')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _exportCategories() {
    final categoryProvider = Provider.of<CategoryProvider>(
      context,
      listen: false,
    );
    final categoriesJson = categoryProvider.exportCategories();

    if (categoriesJson.isEmpty || categoriesJson == '[]') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No custom categories to export')),
      );
      return;
    }

    // Show export dialog with the JSON data
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Categories'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Copy the JSON data below:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SelectableText(
                  categoriesJson,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Categories'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Paste the JSON data below:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 8,
              decoration: InputDecoration(
                hintText: 'Paste JSON data here...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => _importCategories(controller.text),
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  Future<void> _importCategories(String jsonData) async {
    if (jsonData.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter JSON data')));
      return;
    }

    try {
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );
      await categoryProvider.importCategories(jsonData);

      if (mounted) {
        Navigator.of(context).pop(); // Close dialog

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Categories imported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to import categories: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
