import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/budget.dart';
import '../providers/budget_provider.dart';
import '../providers/category_provider.dart';
import '../services/budget_service.dart';
import '../utils/string_utils.dart';
import 'create_budget_screen.dart';
import 'budget_details_screen.dart';
import 'budget_suggestion_screen.dart';

class BudgetManagementScreen extends StatefulWidget {
  const BudgetManagementScreen({Key? key}) : super(key: key);

  @override
  State<BudgetManagementScreen> createState() => _BudgetManagementScreenState();
}

class _BudgetManagementScreenState extends State<BudgetManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BudgetProvider>(context, listen: false).refresh();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget Management'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Active', icon: Icon(Icons.trending_up)),
            Tab(text: 'All', icon: Icon(Icons.list)),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () => _navigateToSuggestions(),
            icon: const Icon(Icons.lightbulb),
            tooltip: 'Budget Suggestions',
          ),
          IconButton(
            onPressed: () => _refreshData(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Consumer<BudgetProvider>(
        builder: (context, budgetProvider, child) {
          if (budgetProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (budgetProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${budgetProvider.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => budgetProvider.refresh(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(budgetProvider),
              _buildActiveBudgetsTab(budgetProvider),
              _buildAllBudgetsTab(budgetProvider),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateBudget(),
        child: const Icon(Icons.add),
        tooltip: 'Create Budget',
      ),
    );
  }

  Widget _buildOverviewTab(BudgetProvider budgetProvider) {
    final statistics = budgetProvider.statistics;
    final alerts = budgetProvider.getBudgetAlerts();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistics cards
          if (statistics != null) _buildStatisticsCards(statistics),

          const SizedBox(height: 24),

          // Budget alerts
          if (alerts.isNotEmpty) ...[
            Text(
              'Budget Alerts',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...alerts.map((alert) => _buildAlertCard(alert)),
            const SizedBox(height: 24),
          ],

          // Quick stats
          _buildQuickStats(budgetProvider),
        ],
      ),
    );
  }

  Widget _buildActiveBudgetsTab(BudgetProvider budgetProvider) {
    final currentBudgets = budgetProvider.currentBudgets;

    if (currentBudgets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No active budgets',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first budget to start tracking spending',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _navigateToCreateBudget(),
              child: const Text('Create Budget'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: currentBudgets.length,
      itemBuilder: (context, index) {
        return BudgetCard(
          budget: currentBudgets[index],
          onTap: () => _navigateToBudgetDetails(currentBudgets[index]),
          onEdit: () => _editBudget(currentBudgets[index]),
          onDelete: () => _deleteBudget(currentBudgets[index]),
        );
      },
    );
  }

  Widget _buildAllBudgetsTab(BudgetProvider budgetProvider) {
    final allBudgets = budgetProvider.budgets;

    if (allBudgets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No budgets created yet',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _navigateToCreateBudget(),
              child: const Text('Create Your First Budget'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: allBudgets.length,
      itemBuilder: (context, index) {
        return BudgetCard(
          budget: allBudgets[index],
          onTap: () => _navigateToBudgetDetails(allBudgets[index]),
          onEdit: () => _editBudget(allBudgets[index]),
          onDelete: () => _deleteBudget(allBudgets[index]),
          showStatus: true,
        );
      },
    );
  }

  Widget _buildStatisticsCards(BudgetStatistics statistics) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Budgets',
                statistics.totalBudgets.toString(),
                Icons.account_balance_wallet,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'On Track',
                statistics.onTrackBudgets.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Near Limit',
                statistics.nearLimitBudgets.toString(),
                Icons.warning,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Exceeded',
                statistics.exceededBudgets.toString(),
                Icons.error,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Budget',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      StringUtils.formatCurrency(statistics.totalBudgetAmount),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Spent',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                    Text(
                      StringUtils.formatCurrency(statistics.totalSpentAmount),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.red[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Remaining',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                    Text(
                      StringUtils.formatCurrency(
                        statistics.totalRemainingAmount,
                      ),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: statistics.overallUsagePercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    statistics.overallUsagePercentage > 0.8
                        ? Colors.red
                        : Colors.blue,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(statistics.overallUsagePercentage * 100).toInt()}% used',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertCard(BudgetAlert alert) {
    Color alertColor;
    IconData alertIcon;

    switch (alert.type) {
      case BudgetAlertType.exceeded:
        alertColor = Colors.red;
        alertIcon = Icons.error;
        break;
      case BudgetAlertType.nearLimit:
        alertColor = Colors.orange;
        alertIcon = Icons.warning;
        break;
      case BudgetAlertType.expiringSoon:
        alertColor = Colors.blue;
        alertIcon = Icons.schedule;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: alertColor.withOpacity(0.1),
      child: ListTile(
        leading: Icon(alertIcon, color: alertColor),
        title: Text(alert.message),
        subtitle: Text(alert.budget.categoryName),
        trailing: TextButton(
          onPressed: () => _navigateToBudgetDetails(alert.budget),
          child: const Text('View'),
        ),
      ),
    );
  }

  Widget _buildQuickStats(BudgetProvider budgetProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Stats',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildQuickStatRow(
                  'On Track Budgets',
                  budgetProvider.onTrackBudgets.length,
                  Colors.green,
                ),
                _buildQuickStatRow(
                  'Near Limit Budgets',
                  budgetProvider.nearLimitBudgets.length,
                  Colors.orange,
                ),
                _buildQuickStatRow(
                  'Exceeded Budgets',
                  budgetProvider.exceededBudgets.length,
                  Colors.red,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatRow(String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateBudget() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CreateBudgetScreen()));
  }

  void _navigateToBudgetDetails(Budget budget) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BudgetDetailsScreen(budget: budget),
      ),
    );
  }

  void _editBudget(Budget budget) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateBudgetScreen(budget: budget),
      ),
    );
  }

  void _deleteBudget(Budget budget) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Budget'),
        content: Text(
          'Are you sure you want to delete the budget for "${budget.categoryName}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<BudgetProvider>(
                context,
                listen: false,
              ).deleteBudget(budget.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Budget for ${budget.categoryName} deleted'),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _refreshData() {
    Provider.of<BudgetProvider>(context, listen: false).refresh();
  }

  void _navigateToSuggestions() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BudgetSuggestionScreen()),
    );
  }
}

class BudgetCard extends StatelessWidget {
  final Budget budget;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final bool showStatus;

  const BudgetCard({
    Key? key,
    required this.budget,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    this.showStatus = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progressColor = _getProgressColor();

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          budget.categoryName,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${budget.period.displayName} Budget',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  if (showStatus) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: progressColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            budget.status.icon,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            budget.status.displayName,
                            style: TextStyle(
                              color: progressColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Spent: ${StringUtils.formatCurrency(budget.spentAmount)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Budget: ${StringUtils.formatCurrency(budget.budgetAmount)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              LinearProgressIndicator(
                value: budget.usagePercentage,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              ),

              const SizedBox(height: 8),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${(budget.usagePercentage * 100).toInt()}% used',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                  Text(
                    '${budget.daysRemaining} days left',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getProgressColor() {
    switch (budget.status) {
      case BudgetStatus.onTrack:
        return Colors.green;
      case BudgetStatus.nearLimit:
        return Colors.orange;
      case BudgetStatus.exceeded:
        return Colors.red;
      case BudgetStatus.expired:
        return Colors.grey;
      case BudgetStatus.inactive:
        return Colors.grey;
    }
  }
}
