import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/budget.dart';
import '../models/transaction.dart';
import '../providers/budget_provider.dart';
import '../providers/category_provider.dart';
import 'budget_suggestion_screen.dart';

class CreateBudgetScreen extends StatefulWidget {
  final Budget? budget; // For editing existing budget

  const CreateBudgetScreen({Key? key, this.budget}) : super(key: key);

  @override
  State<CreateBudgetScreen> createState() => _CreateBudgetScreenState();
}

class _CreateBudgetScreenState extends State<CreateBudgetScreen> {
  final _formKey = GlobalKey<FormState>();
  final _budgetAmountController = TextEditingController();

  String? _selectedCategory;
  BudgetPeriod _selectedPeriod = BudgetPeriod.monthly;
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.budget != null) {
      // Pre-fill form for editing
      _selectedCategory = widget.budget!.categoryName;
      _budgetAmountController.text = widget.budget!.budgetAmount.toString();
      _selectedPeriod = widget.budget!.period;
      if (_selectedPeriod == BudgetPeriod.custom) {
        _customStartDate = widget.budget!.startDate;
        _customEndDate = widget.budget!.endDate;
      }
    }
  }

  @override
  void dispose() {
    _budgetAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.budget != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Budget' : 'Create Budget'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!isEditing)
            TextButton(
              onPressed: () => _navigateToSuggestions(),
              child: const Text('Get Suggestions'),
            ),
          TextButton(
            onPressed: _isLoading ? null : _saveBudget,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(isEditing ? 'Update' : 'Create'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category Selection
              _buildSectionTitle('Category'),
              const SizedBox(height: 8),
              Consumer<CategoryProvider>(
                builder: (context, categoryProvider, child) {
                  final expenseCategories = categoryProvider
                      .getCategoriesByType(TransactionType.expense);

                  return DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: InputDecoration(
                      hintText: 'Select category',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    items: expenseCategories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category.name,
                        child: Row(
                          children: [
                            Text(
                              category.icon,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 8),
                            Text(category.name),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  );
                },
              ),

              const SizedBox(height: 24),

              // Budget Amount
              _buildSectionTitle('Budget Amount'),
              const SizedBox(height: 8),
              TextFormField(
                controller: _budgetAmountController,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter budget amount',
                  prefixText: '₹ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter budget amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Budget Period
              _buildSectionTitle('Budget Period'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: BudgetPeriod.values.map((period) {
                  return ChoiceChip(
                    label: Text(period.displayName),
                    selected: _selectedPeriod == period,
                    onSelected: (selected) {
                      setState(() {
                        _selectedPeriod = period;
                        if (period != BudgetPeriod.custom) {
                          _customStartDate = null;
                          _customEndDate = null;
                        }
                      });
                    },
                  );
                }).toList(),
              ),

              // Custom Date Range (only for custom period)
              if (_selectedPeriod == BudgetPeriod.custom) ...[
                const SizedBox(height: 24),
                _buildSectionTitle('Custom Date Range'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Card(
                        child: InkWell(
                          onTap: _selectStartDate,
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Start Date',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(color: Colors.grey[600]),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _customStartDate != null
                                      ? '${_customStartDate!.day}/${_customStartDate!.month}/${_customStartDate!.year}'
                                      : 'Select date',
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Card(
                        child: InkWell(
                          onTap: _selectEndDate,
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'End Date',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(color: Colors.grey[600]),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _customEndDate != null
                                      ? '${_customEndDate!.day}/${_customEndDate!.month}/${_customEndDate!.year}'
                                      : 'Select date',
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 32),

              // Create/Update Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveBudget,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          isEditing ? 'Update Budget' : 'Create Budget',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _customStartDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _customStartDate = date;
        // Reset end date if it's before start date
        if (_customEndDate != null && _customEndDate!.isBefore(date)) {
          _customEndDate = null;
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _customEndDate ??
          _customStartDate?.add(const Duration(days: 30)) ??
          DateTime.now().add(const Duration(days: 30)),
      firstDate: _customStartDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _customEndDate = date;
      });
    }
  }

  Future<void> _saveBudget() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPeriod == BudgetPeriod.custom) {
      if (_customStartDate == null || _customEndDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Please select both start and end dates for custom period',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final budgetAmount = double.parse(_budgetAmountController.text);
      final budgetProvider = Provider.of<BudgetProvider>(
        context,
        listen: false,
      );

      if (widget.budget != null) {
        // Update existing budget
        final updatedBudget = widget.budget!.copyWith(
          categoryName: _selectedCategory!,
          budgetAmount: budgetAmount,
          period: _selectedPeriod,
          startDate: _selectedPeriod == BudgetPeriod.custom
              ? _customStartDate!
              : widget.budget!.startDate,
          endDate: _selectedPeriod == BudgetPeriod.custom
              ? _customEndDate!
              : widget.budget!.endDate,
          lastUpdated: DateTime.now(),
        );

        await budgetProvider.updateBudget(updatedBudget);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Budget updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        // Create new budget
        await budgetProvider.createBudget(
          categoryName: _selectedCategory!,
          budgetAmount: budgetAmount,
          period: _selectedPeriod,
          customStartDate: _customStartDate,
          customEndDate: _customEndDate,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Budget created successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToSuggestions() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BudgetSuggestionScreen()),
    );
  }
}
