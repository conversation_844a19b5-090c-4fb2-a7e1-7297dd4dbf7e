import 'package:flutter/material.dart';
import '../models/monthly_report.dart';
import '../services/monthly_report_service.dart';
import '../utils/string_utils.dart';

class MonthlyReportScreen extends StatefulWidget {
  const MonthlyReportScreen({Key? key}) : super(key: key);

  @override
  State<MonthlyReportScreen> createState() => _MonthlyReportScreenState();
}

class _MonthlyReportScreenState extends State<MonthlyReportScreen> {
  final MonthlyReportService _reportService = MonthlyReportService();

  List<MonthYear> _availableMonths = [];
  MonthYear? _selectedMonth;
  MonthlyReport? _currentReport;
  MonthlyComparison? _comparison;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAvailableMonths();
  }

  Future<void> _loadAvailableMonths() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final months = await _reportService.getAvailableMonths();
      setState(() {
        _availableMonths = months;
        if (months.isNotEmpty) {
          _selectedMonth = months.first; // Most recent month
          _loadMonthlyReport();
        } else {
          _isLoading = false;
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load available months: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMonthlyReport() async {
    if (_selectedMonth == null) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final report = await _reportService.generateMonthlyReport(
        _selectedMonth!.year,
        _selectedMonth!.month,
      );

      final comparison = await _reportService.generateMonthlyComparison(
        _selectedMonth!.year,
        _selectedMonth!.month,
      );

      setState(() {
        _currentReport = report;
        _comparison = comparison;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load monthly report: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Monthly Reports'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_currentReport != null)
            IconButton(
              onPressed: () => _navigateToDetails(),
              icon: const Icon(Icons.analytics),
              tooltip: 'Detailed Analysis',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAvailableMonths,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_availableMonths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assessment, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No transaction data available',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Start adding transactions to see monthly reports',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAvailableMonths,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Month selector
            _buildMonthSelector(),

            const SizedBox(height: 24),

            if (_currentReport != null) ...[
              // Summary cards
              _buildSummaryCards(),

              const SizedBox(height: 24),

              // Trends comparison
              if (_comparison != null) _buildTrendsSection(),

              const SizedBox(height: 24),

              // Category breakdown
              _buildCategoryBreakdown(),

              const SizedBox(height: 24),

              // Budget performance
              if (_currentReport!.budgetComparisons.isNotEmpty)
                _buildBudgetPerformance(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMonthSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Month',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<MonthYear>(
              value: _selectedMonth,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              items: _availableMonths.map((month) {
                return DropdownMenuItem<MonthYear>(
                  value: month,
                  child: Text(month.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null && value != _selectedMonth) {
                  setState(() {
                    _selectedMonth = value;
                  });
                  _loadMonthlyReport();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final report = _currentReport!;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Income',
                StringUtils.formatCurrency(report.totalIncome),
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Total Expenses',
                StringUtils.formatCurrency(report.totalExpenses),
                Icons.trending_down,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Net Savings',
                StringUtils.formatCurrency(report.netSavings),
                report.isPositiveSavings ? Icons.savings : Icons.warning,
                report.isPositiveSavings ? Colors.blue : Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Savings Rate',
                '${report.savingsRate.toStringAsFixed(1)}%',
                Icons.percent,
                report.savingsRate > 20
                    ? Colors.green
                    : report.savingsRate > 10
                    ? Colors.orange
                    : Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendsSection() {
    final comparison = _comparison!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Month-over-Month Trends',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            if (comparison.previousMonth != null) ...[
              _buildTrendItem(
                'Expenses',
                comparison.expenseChange,
                comparison.expenseChangePercentage,
                false, // Lower is better for expenses
              ),
              const SizedBox(height: 12),
              _buildTrendItem(
                'Income',
                comparison.incomeChange,
                comparison.incomeChangePercentage,
                true, // Higher is better for income
              ),
              const SizedBox(height: 12),
              _buildTrendItem(
                'Savings',
                comparison.savingsChange,
                0.0, // Don't show percentage for savings
                true, // Higher is better for savings
              ),
            ] else ...[
              Text(
                'No previous month data available for comparison',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTrendItem(
    String label,
    double change,
    double percentage,
    bool higherIsBetter,
  ) {
    final isPositive = change > 0;
    final isGoodTrend = higherIsBetter ? isPositive : !isPositive;
    final color = isGoodTrend ? Colors.green : Colors.red;
    final icon = isPositive ? Icons.trending_up : Icons.trending_down;

    return Row(
      children: [
        Expanded(
          child: Text(label, style: Theme.of(context).textTheme.bodyMedium),
        ),
        Row(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              StringUtils.formatCurrency(change.abs()),
              style: TextStyle(color: color, fontWeight: FontWeight.w600),
            ),
            if (percentage != 0.0) ...[
              const SizedBox(width: 4),
              Text(
                '(${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(color: color, fontSize: 12),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryBreakdown() {
    final report = _currentReport!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Breakdown',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Top expense categories
            if (report.expensesByCategory.isNotEmpty) ...[
              Text(
                'Top Expense Categories',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              ...(report.expensesByCategory.entries.toList()
                    ..sort((a, b) => b.value.compareTo(a.value)))
                  .take(5)
                  .map(
                    (entry) => _buildCategoryItem(
                      entry.key,
                      entry.value,
                      report.totalExpenses,
                      Colors.red,
                    ),
                  ),
            ],

            if (report.expensesByCategory.isNotEmpty &&
                report.incomeByCategory.isNotEmpty)
              const SizedBox(height: 16),

            // Top income categories
            if (report.incomeByCategory.isNotEmpty) ...[
              Text(
                'Top Income Categories',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              ...(report.incomeByCategory.entries.toList()
                    ..sort((a, b) => b.value.compareTo(a.value)))
                  .take(5)
                  .map(
                    (entry) => _buildCategoryItem(
                      entry.key,
                      entry.value,
                      report.totalIncome,
                      Colors.green,
                    ),
                  ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(
    String category,
    double amount,
    double total,
    Color color,
  ) {
    final percentage = total > 0 ? (amount / total) * 100 : 0.0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                LinearProgressIndicator(
                  value: percentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    color.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                StringUtils.formatCurrency(amount),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetPerformance() {
    final report = _currentReport!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Budget Performance',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...report.budgetComparisons.map(
              (budget) => _buildBudgetItem(budget),
            ),

            const SizedBox(height: 12),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Average Budget Utilization: ${report.budgetUtilization.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetItem(BudgetComparison budget) {
    final color = budget.isOverBudget ? Colors.red : Colors.green;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                budget.categoryName,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
              Text(
                '${budget.utilizationPercentage.toStringAsFixed(1)}%',
                style: TextStyle(color: color, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: (budget.utilizationPercentage / 100).clamp(0.0, 1.0),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Spent: ${StringUtils.formatCurrency(budget.actualAmount)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
              Text(
                'Budget: ${StringUtils.formatCurrency(budget.budgetAmount)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToDetails() {
    // TODO: Navigate to detailed report screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Detailed analysis coming soon!')),
    );
  }
}
