import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/budget_suggestion_service.dart';
import '../providers/budget_provider.dart';
import '../utils/string_utils.dart';
import '../models/budget.dart';

class BudgetSuggestionScreen extends StatefulWidget {
  const BudgetSuggestionScreen({Key? key}) : super(key: key);

  @override
  State<BudgetSuggestionScreen> createState() => _BudgetSuggestionScreenState();
}

class _BudgetSuggestionScreenState extends State<BudgetSuggestionScreen>
    with SingleTickerProviderStateMixin {
  final BudgetSuggestionService _suggestionService = BudgetSuggestionService();
  
  late TabController _tabController;
  BudgetSuggestionPlan? _suggestionPlan;
  bool _isLoading = true;
  String? _error;
  BudgetApproach _selectedApproach = BudgetApproach.balanced;
  Set<String> _selectedCategories = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSuggestions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final plan = await _suggestionService.generateBudgetSuggestions();
      setState(() {
        _suggestionPlan = plan;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget Suggestions'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_suggestionPlan != null && _selectedCategories.isNotEmpty)
            TextButton(
              onPressed: _createSelectedBudgets,
              child: const Text('Create Budgets'),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Analyzing your spending patterns...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSuggestions,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_suggestionPlan == null) {
      return const Center(child: Text('No suggestions available'));
    }

    return Column(
      children: [
        // Analysis summary
        _buildAnalysisSummary(),
        
        // Approach selector
        _buildApproachSelector(),
        
        // Suggestions content
        Expanded(
          child: _buildSuggestionsContent(),
        ),
      ],
    );
  }

  Widget _buildAnalysisSummary() {
    final plan = _suggestionPlan!;
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'Analysis Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Analysis Period',
                    '${plan.analysisMonths} months',
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Avg Monthly Income',
                    StringUtils.formatCurrency(plan.totalHistoricalIncome),
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Avg Monthly Expenses',
                    StringUtils.formatCurrency(plan.totalHistoricalExpenses),
                    Icons.trending_down,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Historical Savings Rate',
                    '${plan.historicalSavingsRate.toStringAsFixed(1)}%',
                    Icons.savings,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildApproachSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: BudgetApproach.values.map((approach) {
          final isSelected = _selectedApproach == approach;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedApproach = approach;
                  _selectedCategories.clear(); // Clear selections when changing approach
                });
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      approach.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      approach.displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSuggestionsContent() {
    final plan = _suggestionPlan!;
    List<BudgetSuggestion> suggestions;
    
    switch (_selectedApproach) {
      case BudgetApproach.conservative:
        suggestions = plan.conservativePlan;
        break;
      case BudgetApproach.balanced:
        suggestions = plan.balancedPlan;
        break;
      case BudgetApproach.aggressive:
        suggestions = plan.aggressivePlan;
        break;
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Approach description
          _buildApproachDescription(),
          
          const SizedBox(height: 16),
          
          // Projected savings
          _buildProjectedSavings(),
          
          const SizedBox(height: 16),
          
          // Insights
          if (plan.insights.isNotEmpty) ...[
            _buildInsights(),
            const SizedBox(height: 16),
          ],
          
          // Category suggestions
          _buildCategorySuggestions(suggestions),
        ],
      ),
    );
  }

  Widget _buildApproachDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(_selectedApproach.icon, style: const TextStyle(fontSize: 24)),
                const SizedBox(width: 8),
                Text(
                  '${_selectedApproach.displayName} Approach',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _selectedApproach.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectedSavings() {
    final plan = _suggestionPlan!;
    final projectedSavings = plan.getProjectedSavingsForPlan(_selectedApproach);
    final projectedSavingsRate = plan.getProjectedSavingsRateForPlan(_selectedApproach);
    final totalBudget = plan.getTotalBudgetForPlan(_selectedApproach);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Projected Results',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildProjectionItem(
                    'Total Budget',
                    StringUtils.formatCurrency(totalBudget),
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildProjectionItem(
                    'Projected Savings',
                    StringUtils.formatCurrency(projectedSavings),
                    projectedSavings > 0 ? Colors.green : Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildProjectionItem(
                    'Savings Rate',
                    '${projectedSavingsRate.toStringAsFixed(1)}%',
                    projectedSavingsRate > 20 ? Colors.green : 
                    projectedSavingsRate > 10 ? Colors.orange : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectionItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInsights() {
    final insights = _suggestionPlan!.insights;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Key Insights',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...insights.take(3).map((insight) => _buildInsightItem(insight)),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightItem(BudgetInsight insight) {
    Color color;
    IconData icon;
    
    switch (insight.type) {
      case InsightType.positive:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case InsightType.warning:
        color = Colors.orange;
        icon = Icons.warning;
        break;
      case InsightType.info:
        color = Colors.blue;
        icon = Icons.info;
        break;
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  insight.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  insight.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySuggestions(List<BudgetSuggestion> suggestions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Budget Suggestions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _selectedCategories.isEmpty ? _selectAll : _clearAll,
                  child: Text(_selectedCategories.isEmpty ? 'Select All' : 'Clear All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...suggestions.map((suggestion) => _buildSuggestionItem(suggestion)),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(BudgetSuggestion suggestion) {
    final isSelected = _selectedCategories.contains(suggestion.categoryName);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: isSelected ? Colors.blue[50] : null,
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (value) {
          setState(() {
            if (value == true) {
              _selectedCategories.add(suggestion.categoryName);
            } else {
              _selectedCategories.remove(suggestion.categoryName);
            }
          });
        },
        title: Text(
          suggestion.categoryName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(suggestion.reasoning),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.confidence, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Confidence: ${(suggestion.confidence * 100).toInt()}%',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        secondary: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              StringUtils.formatCurrency(suggestion.suggestedAmount),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getPriorityColor(suggestion.priority).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getPriorityLabel(suggestion.priority),
                style: TextStyle(
                  color: _getPriorityColor(suggestion.priority),
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPriorityColor(double priority) {
    if (priority > 0.7) return Colors.red;
    if (priority > 0.5) return Colors.orange;
    return Colors.green;
  }

  String _getPriorityLabel(double priority) {
    if (priority > 0.7) return 'HIGH';
    if (priority > 0.5) return 'MED';
    return 'LOW';
  }

  void _selectAll() {
    setState(() {
      List<BudgetSuggestion> suggestions;
      switch (_selectedApproach) {
        case BudgetApproach.conservative:
          suggestions = _suggestionPlan!.conservativePlan;
          break;
        case BudgetApproach.balanced:
          suggestions = _suggestionPlan!.balancedPlan;
          break;
        case BudgetApproach.aggressive:
          suggestions = _suggestionPlan!.aggressivePlan;
          break;
      }
      _selectedCategories = suggestions.map((s) => s.categoryName).toSet();
    });
  }

  void _clearAll() {
    setState(() {
      _selectedCategories.clear();
    });
  }

  Future<void> _createSelectedBudgets() async {
    if (_selectedCategories.isEmpty) return;

    List<BudgetSuggestion> suggestions;
    switch (_selectedApproach) {
      case BudgetApproach.conservative:
        suggestions = _suggestionPlan!.conservativePlan;
        break;
      case BudgetApproach.balanced:
        suggestions = _suggestionPlan!.balancedPlan;
        break;
      case BudgetApproach.aggressive:
        suggestions = _suggestionPlan!.aggressivePlan;
        break;
    }

    final selectedSuggestions = suggestions
        .where((s) => _selectedCategories.contains(s.categoryName))
        .toList();

    try {
      final budgetProvider = Provider.of<BudgetProvider>(context, listen: false);
      
      for (final suggestion in selectedSuggestions) {
        await budgetProvider.createBudget(
          categoryName: suggestion.categoryName,
          budgetAmount: suggestion.suggestedAmount,
          period: BudgetPeriod.monthly,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Created ${selectedSuggestions.length} budgets successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating budgets: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
