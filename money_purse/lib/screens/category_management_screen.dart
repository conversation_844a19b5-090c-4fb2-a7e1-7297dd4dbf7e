import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart' as app_models;
import '../models/transaction.dart';
import '../providers/category_provider.dart';
import '../widgets/icon_picker.dart';
import 'category_statistics_screen.dart';

class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({Key? key}) : super(key: key);

  @override
  State<CategoryManagementScreen> createState() =>
      _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Categories'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: () => _navigateToStatistics(),
            icon: const Icon(Icons.analytics),
            tooltip: 'View Statistics',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Expenses', icon: Icon(Icons.arrow_downward)),
            Tab(text: 'Income', icon: Icon(Icons.arrow_upward)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          CategoryListView(type: TransactionType.expense),
          CategoryListView(type: TransactionType.income),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddCategoryDialog() {
    final type = _tabController.index == 0
        ? TransactionType.expense
        : TransactionType.income;

    showDialog(
      context: context,
      builder: (context) => AddCategoryDialog(type: type),
    );
  }

  void _navigateToStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CategoryStatisticsScreen()),
    );
  }
}

class CategoryListView extends StatelessWidget {
  final TransactionType type;

  const CategoryListView({Key? key, required this.type}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, provider, child) {
        final categories = provider.getCategoriesByType(type);

        if (categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  type == TransactionType.expense
                      ? Icons.shopping_cart
                      : Icons.account_balance_wallet,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${type.displayName.toLowerCase()} categories yet',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Tap the + button to add a category',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return CategoryTile(category: category);
          },
        );
      },
    );
  }
}

class CategoryTile extends StatelessWidget {
  final app_models.Category category;

  const CategoryTile({Key? key, required this.category}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getCategoryColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(category.icon, style: const TextStyle(fontSize: 20)),
        ),
        title: Text(
          category.name,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          '${category.subcategories.length} subcategories',
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        children: [
          if (category.subcategories.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: category.subcategories.map((subcategory) {
                  return Chip(
                    label: Text(subcategory),
                    backgroundColor: Colors.grey[200],
                  );
                }).toList(),
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showAddSubcategoryDialog(context),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add Subcategory'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor() {
    switch (category.type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.ignored:
        return Colors.grey;
    }
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'edit':
        _showEditCategoryDialog(context);
        break;
      case 'delete':
        _showDeleteConfirmation(context);
        break;
    }
  }

  void _showEditCategoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => EditCategoryDialog(category: category),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<CategoryProvider>(
                context,
                listen: false,
              ).deleteCategory(category.name);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${category.name} deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAddSubcategoryDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Subcategory to ${category.name}'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter subcategory name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final subcategory = controller.text.trim();
              if (subcategory.isNotEmpty) {
                Provider.of<CategoryProvider>(
                  context,
                  listen: false,
                ).addSubcategory(category.name, subcategory);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Subcategory "$subcategory" added')),
                );
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}

class AddCategoryDialog extends StatefulWidget {
  final TransactionType type;

  const AddCategoryDialog({Key? key, required this.type}) : super(key: key);

  @override
  State<AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends State<AddCategoryDialog> {
  final _nameController = TextEditingController();
  final _iconController = TextEditingController();
  final List<String> _subcategories = [];
  final _subcategoryController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _iconController.dispose();
    _subcategoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Add ${widget.type.displayName} Category'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Category Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            GestureDetector(
              onTap: _showIconPicker,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Text(
                      _iconController.text.isEmpty
                          ? '📦'
                          : _iconController.text,
                      style: const TextStyle(fontSize: 24),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _iconController.text.isEmpty
                            ? 'Tap to choose icon'
                            : 'Tap to change icon',
                        style: TextStyle(color: Colors.grey[600], fontSize: 16),
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _subcategoryController,
                    decoration: const InputDecoration(
                      labelText: 'Add Subcategory',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _addSubcategory,
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
            if (_subcategories.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: _subcategories.map((sub) {
                  return Chip(
                    label: Text(sub),
                    onDeleted: () {
                      setState(() {
                        _subcategories.remove(sub);
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(onPressed: _saveCategory, child: const Text('Save')),
      ],
    );
  }

  void _addSubcategory() {
    final subcategory = _subcategoryController.text.trim();
    if (subcategory.isNotEmpty && !_subcategories.contains(subcategory)) {
      setState(() {
        _subcategories.add(subcategory);
        _subcategoryController.clear();
      });
    }
  }

  Future<void> _showIconPicker() async {
    final selectedIcon = await showIconPicker(
      context,
      selectedIcon: _iconController.text.isEmpty ? null : _iconController.text,
    );

    if (selectedIcon != null) {
      setState(() {
        _iconController.text = selectedIcon;
      });
    }
  }

  void _saveCategory() {
    final name = _nameController.text.trim();
    final icon = _iconController.text.trim();

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a category name')),
      );
      return;
    }

    final category = app_models.Category(
      name: name,
      icon: icon.isEmpty ? '📦' : icon,
      type: widget.type,
      subcategories: _subcategories,
    );

    Provider.of<CategoryProvider>(context, listen: false).addCategory(category);
    Navigator.of(context).pop();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Category "$name" added')));
  }
}

class EditCategoryDialog extends StatefulWidget {
  final app_models.Category category;

  const EditCategoryDialog({Key? key, required this.category})
    : super(key: key);

  @override
  State<EditCategoryDialog> createState() => _EditCategoryDialogState();
}

class _EditCategoryDialogState extends State<EditCategoryDialog> {
  late TextEditingController _nameController;
  late TextEditingController _iconController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.category.name);
    _iconController = TextEditingController(text: widget.category.icon);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Category'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Category Name',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _iconController,
            decoration: const InputDecoration(
              labelText: 'Icon (emoji)',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(onPressed: _saveChanges, child: const Text('Save')),
      ],
    );
  }

  void _saveChanges() {
    final name = _nameController.text.trim();
    final icon = _iconController.text.trim();

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a category name')),
      );
      return;
    }

    final updatedCategory = app_models.Category(
      name: name,
      icon: icon.isEmpty ? widget.category.icon : icon,
      type: widget.category.type,
      subcategories: widget.category.subcategories,
    );

    Provider.of<CategoryProvider>(
      context,
      listen: false,
    ).updateCategory(widget.category.name, updatedCategory);

    Navigator.of(context).pop();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Category updated')));
  }
}
