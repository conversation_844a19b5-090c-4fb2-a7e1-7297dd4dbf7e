import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/budget.dart';
import '../models/transaction.dart';
import '../providers/budget_provider.dart';
import '../services/database_service.dart';
import '../utils/string_utils.dart';
import 'create_budget_screen.dart';

class BudgetDetailsScreen extends StatefulWidget {
  final Budget budget;

  const BudgetDetailsScreen({Key? key, required this.budget}) : super(key: key);

  @override
  State<BudgetDetailsScreen> createState() => _BudgetDetailsScreenState();
}

class _BudgetDetailsScreenState extends State<BudgetDetailsScreen> {
  List<Transaction> _categoryTransactions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategoryTransactions();
  }

  Future<void> _loadCategoryTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final databaseService = DatabaseService();
      final allTransactions = await databaseService.getAllTransactions();
      
      // Filter transactions for this category and budget period
      _categoryTransactions = allTransactions.where((transaction) {
        return transaction.category == widget.budget.categoryName &&
               transaction.type == TransactionType.expense &&
               transaction.date.isAfter(widget.budget.startDate) &&
               transaction.date.isBefore(widget.budget.endDate);
      }).toList();
      
      // Sort by date (newest first)
      _categoryTransactions.sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading transactions: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final budget = widget.budget;
    final progressColor = _getProgressColor(budget.status);

    return Scaffold(
      appBar: AppBar(
        title: Text(budget.categoryName),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: () => _editBudget(),
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Budget',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'renew':
                  _renewBudget();
                  break;
                case 'delete':
                  _deleteBudget();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'renew',
                child: Row(
                  children: [
                    Icon(Icons.refresh, size: 20),
                    SizedBox(width: 8),
                    Text('Renew Budget'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete Budget', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadCategoryTransactions,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Budget Overview Card
              _buildBudgetOverviewCard(budget, progressColor),
              
              const SizedBox(height: 24),
              
              // Budget Statistics
              _buildBudgetStatistics(budget),
              
              const SizedBox(height: 24),
              
              // Spending Progress
              _buildSpendingProgress(budget, progressColor),
              
              const SizedBox(height: 24),
              
              // Recent Transactions
              _buildRecentTransactions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBudgetOverviewCard(Budget budget, Color progressColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: progressColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: progressColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        budget.categoryName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${budget.period.displayName} Budget',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: progressColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(budget.status.icon, style: const TextStyle(fontSize: 14)),
                      const SizedBox(width: 4),
                      Text(
                        budget.status.displayName,
                        style: TextStyle(
                          color: progressColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Budget amounts
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Budget Amount',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        StringUtils.formatCurrency(budget.budgetAmount),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Spent Amount',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        StringUtils.formatCurrency(budget.spentAmount),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Remaining',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        StringUtils.formatCurrency(budget.remainingAmount),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: budget.remainingAmount >= 0 ? Colors.green[600] : Colors.red[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress bar
            LinearProgressIndicator(
              value: budget.usagePercentage,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: 8,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              '${(budget.usagePercentage * 100).toInt()}% of budget used',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetStatistics(Budget budget) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Budget Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Days Elapsed',
                    budget.daysElapsed.toString(),
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Days Remaining',
                    budget.daysRemaining.toString(),
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Total Days',
                    budget.totalDays.toString(),
                    Icons.date_range,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Daily Rate',
                    StringUtils.formatCurrency(budget.dailySpendingRate),
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Projected',
                    StringUtils.formatCurrency(budget.projectedSpending),
                    Icons.insights,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Transactions',
                    _categoryTransactions.length.toString(),
                    Icons.receipt,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSpendingProgress(Budget budget, Color progressColor) {
    final daysProgress = budget.totalDays > 0 ? budget.daysElapsed / budget.totalDays : 0.0;
    final spendingProgress = budget.usagePercentage;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Spending vs Time Progress',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Time progress
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Time Progress',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      '${(daysProgress * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: daysProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Spending progress
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Spending Progress',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      '${(spendingProgress * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: spendingProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Analysis
            if (spendingProgress > daysProgress + 0.1) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'You\'re spending faster than expected. Consider slowing down.',
                        style: TextStyle(color: Colors.orange[700], fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (spendingProgress < daysProgress - 0.1) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Great job! You\'re spending within your budget timeline.',
                        style: TextStyle(color: Colors.green[700], fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_categoryTransactions.length} total',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_isLoading) ...[
              const Center(child: CircularProgressIndicator()),
            ] else if (_categoryTransactions.isEmpty) ...[
              Center(
                child: Column(
                  children: [
                    Icon(Icons.receipt_long, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'No transactions yet',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _categoryTransactions.take(10).length, // Show only recent 10
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final transaction = _categoryTransactions[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      backgroundColor: Colors.red[50],
                      child: Icon(Icons.arrow_downward, color: Colors.red[600], size: 20),
                    ),
                    title: Text(
                      transaction.description.isNotEmpty 
                          ? transaction.description 
                          : 'Expense',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: Text(
                      '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    trailing: Text(
                      StringUtils.formatCurrency(transaction.amount),
                      style: TextStyle(
                        color: Colors.red[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              ),
              
              if (_categoryTransactions.length > 10) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton(
                    onPressed: () {
                      // TODO: Navigate to full transaction list
                    },
                    child: Text('View all ${_categoryTransactions.length} transactions'),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Color _getProgressColor(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.onTrack:
        return Colors.green;
      case BudgetStatus.nearLimit:
        return Colors.orange;
      case BudgetStatus.exceeded:
        return Colors.red;
      case BudgetStatus.expired:
        return Colors.grey;
      case BudgetStatus.inactive:
        return Colors.grey;
    }
  }

  void _editBudget() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateBudgetScreen(budget: widget.budget),
      ),
    ).then((_) {
      // Refresh data when returning from edit screen
      _loadCategoryTransactions();
    });
  }

  void _renewBudget() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Renew Budget'),
        content: Text('Create a new ${widget.budget.period.displayName.toLowerCase()} budget for ${widget.budget.categoryName} with the same amount?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<BudgetProvider>(context, listen: false).renewBudget(widget.budget);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Budget renewed successfully')),
              );
            },
            child: const Text('Renew'),
          ),
        ],
      ),
    );
  }

  void _deleteBudget() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Budget'),
        content: Text('Are you sure you want to delete the budget for "${widget.budget.categoryName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<BudgetProvider>(context, listen: false).deleteBudget(widget.budget.id);
              Navigator.of(context).pop(); // Go back to budget list
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Budget for ${widget.budget.categoryName} deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
