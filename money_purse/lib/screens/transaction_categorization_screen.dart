import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart';
import '../models/transaction.dart';
import '../providers/transaction_provider.dart';
import '../services/transaction_parser.dart';
import '../utils/string_utils.dart';

class TransactionCategorizationScreen extends StatefulWidget {
  const TransactionCategorizationScreen({Key? key}) : super(key: key);

  @override
  State<TransactionCategorizationScreen> createState() => _TransactionCategorizationScreenState();
}

class _TransactionCategorizationScreenState extends State<TransactionCategorizationScreen> {
  int _currentIndex = 0;
  PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Review Transactions'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<TransactionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.refresh(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final pendingTransactions = provider.pendingTransactions;

          if (pendingTransactions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, size: 64, color: Colors.green[300]),
                  const SizedBox(height: 16),
                  Text(
                    'All transactions reviewed!',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No new transactions to categorize.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => provider.scanForNewTransactions(),
                    child: const Text('Scan for New Transactions'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Progress indicator
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Transaction ${_currentIndex + 1} of ${pendingTransactions.length}',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          '${pendingTransactions.length - _currentIndex - 1} remaining',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: (_currentIndex + 1) / pendingTransactions.length,
                      backgroundColor: Colors.grey[300],
                    ),
                  ],
                ),
              ),
              
              // Transaction cards
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  itemCount: pendingTransactions.length,
                  itemBuilder: (context, index) {
                    return TransactionCategorizationCard(
                      transaction: pendingTransactions[index],
                      onCategorized: () => _moveToNext(),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _moveToNext() {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    if (_currentIndex < provider.pendingTransactions.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // All transactions processed, go back
      Navigator.of(context).pop();
    }
  }
}

class TransactionCategorizationCard extends StatefulWidget {
  final ParsedTransaction transaction;
  final VoidCallback onCategorized;

  const TransactionCategorizationCard({
    Key? key,
    required this.transaction,
    required this.onCategorized,
  }) : super(key: key);

  @override
  State<TransactionCategorizationCard> createState() => _TransactionCategorizationCardState();
}

class _TransactionCategorizationCardState extends State<TransactionCategorizationCard> {
  TransactionType? _selectedType;
  Category? _selectedCategory;
  String? _selectedSubcategory;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.transaction.type;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction details card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getTypeColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          StringUtils.formatCurrency(widget.transaction.amount),
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: _getTypeColor(),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${(widget.transaction.confidence * 100).toInt()}% confidence',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  Text(
                    widget.transaction.description,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  
                  if (widget.transaction.merchant != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.store, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          widget.transaction.merchant!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                  
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(widget.transaction.date),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  
                  if (widget.transaction.smsAddress != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.sms, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          'From: ${widget.transaction.smsAddress}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Transaction type selection
          Text(
            'Transaction Type',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Row(
            children: TransactionType.values.where((type) => type != TransactionType.ignored).map((type) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(type.displayName),
                    avatar: Text(type.icon),
                    selected: _selectedType == type,
                    onSelected: (selected) {
                      setState(() {
                        _selectedType = selected ? type : null;
                        _selectedCategory = null;
                        _selectedSubcategory = null;
                      });
                    },
                  ),
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Category selection
          if (_selectedType != null) ...[
            Text(
              'Category',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: Categories.getCategoriesByType(_selectedType!).map((category) {
                return ChoiceChip(
                  label: Text(category.name),
                  avatar: Text(category.icon),
                  selected: _selectedCategory == category,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = selected ? category : null;
                      _selectedSubcategory = null;
                    });
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
          ],
          
          // Subcategory selection
          if (_selectedCategory != null) ...[
            Text(
              'Subcategory',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedCategory!.subcategories.map((subcategory) {
                return ChoiceChip(
                  label: Text(subcategory),
                  selected: _selectedSubcategory == subcategory,
                  onSelected: (selected) {
                    setState(() {
                      _selectedSubcategory = selected ? subcategory : null;
                    });
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
          ],
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _rejectTransaction(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                  ),
                  child: const Text('Ignore'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _canAccept() ? () => _acceptTransaction() : null,
                  child: const Text('Accept'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getTypeColor() {
    switch (widget.transaction.type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.ignored:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  bool _canAccept() {
    return _selectedType != null && _selectedCategory != null;
  }

  void _acceptTransaction() {
    if (!_canAccept()) return;

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    provider.acceptPendingTransaction(
      parsedTransaction: widget.transaction,
      category: _selectedCategory!.name,
      subcategory: _selectedSubcategory,
    );
    
    widget.onCategorized();
  }

  void _rejectTransaction() {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    provider.rejectPendingTransaction(widget.transaction);
    widget.onCategorized();
  }
}
