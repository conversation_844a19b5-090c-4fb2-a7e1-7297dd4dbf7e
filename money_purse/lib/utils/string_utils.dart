class StringUtils {
  /// Convert string to title case (first letter of each word capitalized)
  static String toTitleCase(String text) {
    if (text.isEmpty) return text;
    
    return text
        .toLowerCase()
        .split(' ')
        .map((word) => word.isEmpty ? word : word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  /// Clean and normalize text for comparison
  static String normalize(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special characters
        .replaceAll(RegExp(r'\s+'), ' ') // Replace multiple spaces with single space
        .trim();
  }

  /// Extract numbers from text
  static List<double> extractNumbers(String text) {
    final numbers = <double>[];
    final regex = RegExp(r'\d+(?:\.\d+)?');
    final matches = regex.allMatches(text);
    
    for (final match in matches) {
      final numberStr = match.group(0);
      if (numberStr != null) {
        final number = double.tryParse(numberStr);
        if (number != null) {
          numbers.add(number);
        }
      }
    }
    
    return numbers;
  }

  /// Check if text contains any of the given keywords
  static bool containsAny(String text, List<String> keywords) {
    final normalizedText = normalize(text);
    return keywords.any((keyword) => normalizedText.contains(normalize(keyword)));
  }

  /// Format currency amount
  static String formatCurrency(double amount, {String symbol = '₹'}) {
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Truncate text to specified length with ellipsis
  static String truncate(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }

  /// Remove common SMS prefixes and suffixes
  static String cleanSmsText(String text) {
    return text
        .replaceAll(RegExp(r'^(Dear Customer,?|Hi,?)\s*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s*(Thank you|Thanks|Regards).*$', caseSensitive: false), '')
        .trim();
  }

  /// Check if string is likely a phone number
  static bool isPhoneNumber(String text) {
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,15}$');
    return phoneRegex.hasMatch(text.replaceAll(RegExp(r'\s'), ''));
  }

  /// Check if string is likely an email
  static bool isEmail(String text) {
    final emailRegex = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w+$');
    return emailRegex.hasMatch(text);
  }

  /// Extract UPI ID from text
  static String? extractUpiId(String text) {
    final upiRegex = RegExp(r'[\w\.-]+@[\w\.-]+', caseSensitive: false);
    final match = upiRegex.firstMatch(text);
    return match?.group(0);
  }

  /// Check if text contains Indian currency patterns
  static bool containsIndianCurrency(String text) {
    final currencyRegex = RegExp(r'(₹|rs\.?|inr|rupees?)', caseSensitive: false);
    return currencyRegex.hasMatch(text);
  }

  /// Format Indian currency with proper comma separation
  static String formatIndianCurrency(double amount) {
    final formatter = RegExp(r'(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?');
    String result = amount.toStringAsFixed(2);
    return '₹${result.replaceAllMapped(formatter, (match) => '${match[1]},')}';
  }
}
