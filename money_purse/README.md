# Money Purse - Flutter Expense Tracker

A comprehensive Flutter expense tracker app with SMS transaction detection and manual entry capabilities.

## 🚀 Features

### ✅ **Implemented Features**

1. **📱 Manual Transaction Entry**
   - Add income and expenses manually
   - Select from predefined categories and subcategories
   - Set custom date and time
   - Add merchant information
   - Form validation and error handling

2. **📨 SMS Transaction Detection**
   - Mock SMS parsing for demonstration
   - Support for major Indian banks and payment services
   - Automatic transaction type detection (income/expense)
   - Confidence scoring for parsed transactions

3. **🔐 Permission Management**
   - SMS permission request flow
   - User-friendly permission screens
   - Proper error handling

4. **💾 Data Storage**
   - SQLite database for local storage
   - Transaction CRUD operations
   - Processed SMS tracking
   - Analytics queries

5. **🎨 User Interface**
   - **Home Screen**: Balance overview, recent transactions, quick actions
   - **Add Transaction**: Manual entry form with categories
   - **Transaction History**: Searchable and filterable list
   - **Categorization**: Review and categorize SMS transactions
   - **Settings**: App configuration and data management

6. **📊 Categories & Analytics**
   - Predefined expense categories (Food, Transport, Shopping, etc.)
   - Income categories (Salary, Freelance, Business, etc.)
   - Real-time balance calculations
   - Transaction statistics

## 📱 How to Use

### Adding Manual Transactions

1. **Tap the "+" button** on the home screen or **"Add Manual"** in quick actions
2. **Select transaction type**: Expense or Income
3. **Enter amount**: Required field with validation
4. **Add description**: Brief description of the transaction
5. **Choose category**: Select from predefined categories
6. **Pick subcategory**: Optional subcategory selection
7. **Set date & time**: Default to current, can be customized
8. **Add merchant**: Optional merchant/vendor name
9. **Save**: Transaction is added to your records

### SMS Transaction Detection

1. **Grant SMS permission** when prompted
2. **Scan SMS**: Use "Scan SMS" in quick actions
3. **Review transactions**: Check detected transactions in "Review SMS"
4. **Categorize**: Assign categories to detected transactions
5. **Accept or ignore**: Choose which transactions to keep

## 🚀 Getting Started

1. **Clone the repository**
2. **Install dependencies**: `flutter pub get`
3. **Run the app**: `flutter run`
4. **Grant permissions** when prompted
5. **Start adding transactions** manually or scan SMS

## 📝 Notes

- SMS functionality uses mock data for demonstration
- All data is stored locally on the device
- No data is transmitted to external servers
- App requires Android SMS permissions for full functionality
